import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PostsController } from './posts.controller';
import { PostsService } from './posts.service';
import { PostRepository, CommunityRepository, UserRepository } from '@/database/repositories';
import { SecurityModule } from '@/security/security.module';
import {
  PostEntity,
  PostVoteEntity,
  UserEntity,
  CommunityEntity,
  CommunityMemberEntity,
  FishingSpotEntity,
  TagEntity
} from '@/database/entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PostEntity,
      PostVoteEntity,
      UserEntity,
      CommunityEntity,
      CommunityMemberEntity,
      FishingSpotEntity,
      TagEntity,
    ]),
    SecurityModule,
  ],
  controllers: [PostsController],
  providers: [
    PostsService,
    PostRepository,
    CommunityRepository,
    UserRepository,
  ],
  exports: [PostsService, PostRepository],
})
export class PostsModule { }
