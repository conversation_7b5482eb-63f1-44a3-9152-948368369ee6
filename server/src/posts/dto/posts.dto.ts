import { 
  IsString, 
  IsEnum, 
  IsOptional, 
  IsUUID, 
  <PERSON>Int, 
  Min, 
  Max, 
  Length,
  IsNotEmpty,
  ValidateNested,
  IsArray,
  IsBoolean
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { 
  PostStatus,
  VoteType,
  Post,
  User,
  Community,
  FishingSpot,
  Tag
} from '@/types/global';

// Post Creation DTO
export class CreatePostDto {
  @IsString()
  @IsNotEmpty()
  @Length(3, 200)
  title: string;

  @IsString()
  @IsOptional()
  @Length(1, 10000)
  content?: string;

  @IsUUID()
  communityId: string;

  @IsUUID()
  @IsOptional()
  fishingSpotId?: string;

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  tagNames?: string[];

  @IsEnum(PostStatus)
  @IsOptional()
  status?: PostStatus = PostStatus.Published;
}

// Post Update DTO
export class UpdatePostDto {
  @IsString()
  @IsOptional()
  @Length(3, 200)
  title?: string;

  @IsString()
  @IsOptional()
  @Length(1, 10000)
  content?: string;

  @IsUUID()
  @IsOptional()
  fishingSpotId?: string;

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  tagNames?: string[];

  @IsEnum(PostStatus)
  @IsOptional()
  status?: PostStatus;
}

// Post Vote DTO
export class VotePostDto {
  @IsEnum(VoteType)
  voteType: VoteType;
}

// Post Search Query DTO
export class PostSearchQueryDto {
  @IsOptional()
  @IsString()
  @Length(1, 100)
  search?: string;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsUUID()
  communityId?: string;

  @IsOptional()
  @IsUUID()
  authorId?: string;

  @IsOptional()
  @IsEnum(PostStatus)
  status?: PostStatus;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  @IsEnum(['newest', 'oldest', 'popular', 'trending'])
  sortBy?: 'newest' | 'oldest' | 'popular' | 'trending' = 'newest';
}

// Response DTOs
export class PublicUserProfileDto {
  id: string;
  uname: string;
  profile: {
    displayName: string;
    bio?: string;
    location?: string;
    website?: string;
    pic?: string;
    banner?: string;
  };
  activity: {
    postsCount: number;
    commentsCount: number;
    followersCount: number;
    followingCount: number;
  };
  createdAt: Date;
}

export class CommunityBasicDto {
  id: string;
  name: string;
  description: string;
  pic?: string;
  banner?: string;
}

export class FishingSpotBasicDto {
  id: string;
  name: string;
  description: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  typesOfFish: string[];
}

export class TagDto {
  id: string;
  name: string;
  description?: string;
}

export class PostResponseDto {
  id: string;
  title: string;
  status: PostStatus;
  content?: string;
  author: PublicUserProfileDto;
  community: CommunityBasicDto;
  fishingSpot?: FishingSpotBasicDto;
  tags: TagDto[];
  upvotes: number;
  downvotes: number;
  views: number;
  userVote?: VoteType;
  createdAt: Date;
  updatedAt: Date;
}

export class PostListResponseDto {
  posts: PostResponseDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class PostVoteStatsDto {
  upvotes: number;
  downvotes: number;
  userVote?: VoteType;
}

// Pagination Query DTO (reusable)
export class PaginationQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}

// Post Moderation DTO
export class ModeratePostDto {
  @IsEnum(PostStatus)
  status: PostStatus;

  @IsString()
  @IsOptional()
  @Length(1, 500)
  reason?: string;
}
