import { Test, TestingModule } from '@nestjs/testing';
import { PostsController } from './posts.controller';
import { PostsService } from './posts.service';
import { JwtAuthGuard } from '@/auth/guards/jwt.guard';
import { CsrfGuard } from '@/security/guards/csrf.guard';
import {
  CreatePostDto,
  UpdatePostDto,
  VotePostDto,
  PostSearchQueryDto,
  ModeratePostDto,
  PostResponseDto,
  PostListResponseDto,
  PostVoteStatsDto,
  PaginationQueryDto
} from './dto/posts.dto';
import { PostStatus, VoteType, User, UserStatus } from '@/types/global';

describe('PostsController', () => {
  let controller: PostsController;
  let service: jest.Mocked<PostsService>;

  const mockUser: User = {
    id: 'user-1',
    uname: 'testuser',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'Test bio',
      location: 'Test City',
      website: 'https://test.com',
      pic: 'test.jpg',
      banner: 'banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'Light',
      notificationPreferences: [],
      emailDigest: 'Weekly',
      contentVisibility: 'Public',
      twoFactorEnabled: false,
      sessionTimeout: 'Medium',
    },
    activity: {
      postsToday: 5,
      commentsToday: 10,
      votesToday: 15,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPostResponse: PostResponseDto = {
    id: 'post-1',
    title: 'Test Post',
    status: PostStatus.Published,
    content: 'This is a test post',
    author: {
      id: mockUser.id,
      uname: mockUser.uname,
      profile: mockUser.profile,
      activity: {
        postsCount: 5,
        commentsCount: 10,
        followersCount: 0,
        followingCount: 0,
      },
      createdAt: mockUser.createdAt,
    },
    community: {
      id: 'community-1',
      name: 'Test Community',
      description: 'A test community',
      pic: 'community.jpg',
      banner: 'banner.jpg',
    },
    tags: [],
    upvotes: 5,
    downvotes: 1,
    views: 100,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockJwtAuthGuard = {
    canActivate: jest.fn(() => true),
  };

  const mockCsrfGuard = {
    canActivate: jest.fn(() => true),
  };

  beforeEach(async () => {
    const mockService = {
      createPost: jest.fn(),
      getPost: jest.fn(),
      updatePost: jest.fn(),
      deletePost: jest.fn(),
      getPosts: jest.fn(),
      getPostsByCommunity: jest.fn(),
      getPostsByAuthor: jest.fn(),
      voteOnPost: jest.fn(),
      removeVote: jest.fn(),
      getPostVoteStats: jest.fn(),
      moderatePost: jest.fn(),
      updatePostStatus: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [PostsController],
      providers: [
        {
          provide: PostsService,
          useValue: mockService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(CsrfGuard)
      .useValue(mockCsrfGuard)
      .compile();

    controller = module.get<PostsController>(PostsController);
    service = module.get(PostsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getPosts', () => {
    it('should return posts list', async () => {
      const query: PostSearchQueryDto = { page: 1, limit: 20 };
      const expectedResult: PostListResponseDto = {
        posts: [mockPostResponse],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      service.getPosts.mockResolvedValue(expectedResult);

      const result = await controller.getPosts(query, { user: mockUser });

      expect(result).toEqual(expectedResult);
      expect(service.getPosts).toHaveBeenCalledWith(query, mockUser.id);
    });

    it('should work without authenticated user', async () => {
      const query: PostSearchQueryDto = { page: 1, limit: 20 };
      const expectedResult: PostListResponseDto = {
        posts: [mockPostResponse],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      service.getPosts.mockResolvedValue(expectedResult);

      const result = await controller.getPosts(query, {});

      expect(result).toEqual(expectedResult);
      expect(service.getPosts).toHaveBeenCalledWith(query, undefined);
    });
  });

  describe('createPost', () => {
    it('should create a post', async () => {
      const createDto: CreatePostDto = {
        title: 'New Post',
        content: 'This is a new post',
        communityId: 'community-1',
      };

      service.createPost.mockResolvedValue(mockPostResponse);

      const result = await controller.createPost(createDto, { user: mockUser });

      expect(result).toEqual(mockPostResponse);
      expect(service.createPost).toHaveBeenCalledWith(createDto, mockUser.id);
    });
  });

  describe('getPost', () => {
    it('should return a single post', async () => {
      service.getPost.mockResolvedValue(mockPostResponse);

      const result = await controller.getPost('post-1', { user: mockUser });

      expect(result).toEqual(mockPostResponse);
      expect(service.getPost).toHaveBeenCalledWith('post-1', mockUser.id);
    });

    it('should work without authenticated user', async () => {
      service.getPost.mockResolvedValue(mockPostResponse);

      const result = await controller.getPost('post-1', {});

      expect(result).toEqual(mockPostResponse);
      expect(service.getPost).toHaveBeenCalledWith('post-1', undefined);
    });
  });

  describe('updatePost', () => {
    it('should update a post', async () => {
      const updateDto: UpdatePostDto = {
        title: 'Updated Post',
        content: 'Updated content',
      };

      const updatedPost = { ...mockPostResponse, ...updateDto };
      service.updatePost.mockResolvedValue(updatedPost);

      const result = await controller.updatePost('post-1', updateDto, { user: mockUser });

      expect(result).toEqual(updatedPost);
      expect(service.updatePost).toHaveBeenCalledWith('post-1', updateDto, mockUser.id);
    });
  });

  describe('deletePost', () => {
    it('should delete a post', async () => {
      service.deletePost.mockResolvedValue(undefined);

      await controller.deletePost('post-1', { user: mockUser });

      expect(service.deletePost).toHaveBeenCalledWith('post-1', mockUser.id);
    });
  });

  describe('voteOnPost', () => {
    it('should vote on a post', async () => {
      const voteDto: VotePostDto = { voteType: VoteType.Upvote };
      const expectedStats: PostVoteStatsDto = {
        upvotes: 6,
        downvotes: 1,
        userVote: VoteType.Upvote,
      };

      service.voteOnPost.mockResolvedValue(expectedStats);

      const result = await controller.voteOnPost('post-1', voteDto, { user: mockUser });

      expect(result).toEqual(expectedStats);
      expect(service.voteOnPost).toHaveBeenCalledWith('post-1', voteDto, mockUser.id);
    });
  });

  describe('removeVote', () => {
    it('should remove vote from a post', async () => {
      const expectedStats: PostVoteStatsDto = {
        upvotes: 4,
        downvotes: 1,
      };

      service.removeVote.mockResolvedValue(expectedStats);

      const result = await controller.removeVote('post-1', { user: mockUser });

      expect(result).toEqual(expectedStats);
      expect(service.removeVote).toHaveBeenCalledWith('post-1', mockUser.id);
    });
  });

  describe('getPostVoteStats', () => {
    it('should return post vote statistics', async () => {
      const expectedStats: PostVoteStatsDto = {
        upvotes: 5,
        downvotes: 1,
        userVote: VoteType.Upvote,
      };

      service.getPostVoteStats.mockResolvedValue(expectedStats);

      const result = await controller.getPostVoteStats('post-1', { user: mockUser });

      expect(result).toEqual(expectedStats);
      expect(service.getPostVoteStats).toHaveBeenCalledWith('post-1', mockUser.id);
    });
  });

  describe('getPostsByCommunity', () => {
    it('should return posts by community', async () => {
      const query: PaginationQueryDto = { page: 1, limit: 20 };
      const expectedResult: PostListResponseDto = {
        posts: [mockPostResponse],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      service.getPostsByCommunity.mockResolvedValue(expectedResult);

      const result = await controller.getPostsByCommunity('community-1', query, { user: mockUser });

      expect(result).toEqual(expectedResult);
      expect(service.getPostsByCommunity).toHaveBeenCalledWith('community-1', query, mockUser.id);
    });
  });

  describe('getPostsByAuthor', () => {
    it('should return posts by author', async () => {
      const query: PaginationQueryDto = { page: 1, limit: 20 };
      const expectedResult: PostListResponseDto = {
        posts: [mockPostResponse],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      service.getPostsByAuthor.mockResolvedValue(expectedResult);

      const result = await controller.getPostsByAuthor('user-1', query, { user: mockUser });

      expect(result).toEqual(expectedResult);
      expect(service.getPostsByAuthor).toHaveBeenCalledWith('user-1', query, mockUser.id);
    });
  });

  describe('moderatePost', () => {
    it('should moderate a post', async () => {
      const moderateDto: ModeratePostDto = {
        status: PostStatus.Flagged,
        reason: 'Inappropriate content',
      };

      const moderatedPost = { ...mockPostResponse, status: PostStatus.Flagged };
      service.moderatePost.mockResolvedValue(moderatedPost);

      const result = await controller.moderatePost('post-1', moderateDto, { user: mockUser });

      expect(result).toEqual(moderatedPost);
      expect(service.moderatePost).toHaveBeenCalledWith('post-1', moderateDto, mockUser.id);
    });
  });
});
