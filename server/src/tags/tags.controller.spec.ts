import { Test, TestingModule } from '@nestjs/testing';
import { TagsController } from './tags.controller';
import { TagsService } from './tags.service';
import { TagStatus, User } from '@/types/global';
import {
  CreateTagDto,
  CreateMultipleTagsDto,
  UpdateTagDto,
  ModerateTagDto,
  TagSearchQueryDto,
  TagSuggestionsQueryDto,
  BulkTagOperationDto,
  TagResponseDto,
  TagListResponseDto,
  TagStatsResponseDto,
  TagSuggestionsResponseDto,
  BulkTagOperationResponseDto,
} from './dto/tags.dto';

describe('TagsController', () => {
  let controller: TagsController;
  let tagsService: jest.Mocked<TagsService>;

  const mockUser: User = {
    id: 'user-1',
    uname: 'testuser',
    email: '<EMAIL>',
    emailVerified: true,
    status: 'Active' as any,
    profile: { displayName: 'Test User' } as any,
    prefs: {} as any,
    activity: {} as any,
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: null,
    lastLoginAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockTagResponse: TagResponseDto = {
    name: 'largemouth-bass',
    displayName: 'Largemouth Bass',
    description: 'A popular game fish',
    category: 'species',
    color: '#3B82F6',
    status: TagStatus.Pending,
    usageCount: 0,
    createdBy: {
      id: 'user-1',
      username: 'testuser',
      displayName: 'Test User',
    },
    moderatedBy: undefined,
    createdAt: new Date(),
    updatedAt: new Date(),
    moderatedAt: undefined,
  };

  beforeEach(async () => {
    const mockTagsService = {
      createTag: jest.fn(),
      createMultipleTags: jest.fn(),
      getOrCreateTags: jest.fn(),
      getTag: jest.fn(),
      searchTags: jest.fn(),
      updateTag: jest.fn(),
      moderateTag: jest.fn(),
      getTagStats: jest.fn(),
      getTagSuggestions: jest.fn(),
      bulkModerate: jest.fn(),
      incrementTagUsage: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [TagsController],
      providers: [
        {
          provide: TagsService,
          useValue: mockTagsService,
        },
      ],
    }).compile();

    controller = module.get<TagsController>(TagsController);
    tagsService = module.get(TagsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getTags', () => {
    it('should return paginated tags list', async () => {
      const query: TagSearchQueryDto = {
        search: 'bass',
        limit: 10,
        offset: 0,
      };

      const mockResponse: TagListResponseDto = {
        tags: [mockTagResponse],
        total: 1,
        limit: 10,
        offset: 0,
        hasMore: false,
      };

      tagsService.searchTags.mockResolvedValue(mockResponse);

      const result = await controller.getTags(query);

      expect(tagsService.searchTags).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getTagStats', () => {
    it('should return tag statistics', async () => {
      const mockStats: TagStatsResponseDto = {
        totalTags: 100,
        approvedTags: 80,
        pendingTags: 15,
        rejectedTags: 5,
        categoryCounts: { species: 50, technique: 30 },
        recentTags: [mockTagResponse],
      };

      tagsService.getTagStats.mockResolvedValue(mockStats);

      const result = await controller.getTagStats();

      expect(tagsService.getTagStats).toHaveBeenCalled();
      expect(result).toEqual(mockStats);
    });
  });

  describe('getTagSuggestions', () => {
    it('should return tag suggestions', async () => {
      const query: TagSuggestionsQueryDto = {
        query: 'bass',
        limit: 5,
      };

      const mockSuggestions: TagSuggestionsResponseDto = {
        suggestions: ['largemouth-bass', 'smallmouth-bass'],
        query: 'bass',
      };

      tagsService.getTagSuggestions.mockResolvedValue(mockSuggestions);

      const result = await controller.getTagSuggestions(query);

      expect(tagsService.getTagSuggestions).toHaveBeenCalledWith('bass', 5);
      expect(result).toEqual(mockSuggestions);
    });
  });

  describe('getTag', () => {
    it('should return specific tag', async () => {
      tagsService.getTag.mockResolvedValue(mockTagResponse);

      const result = await controller.getTag('largemouth-bass');

      expect(tagsService.getTag).toHaveBeenCalledWith('largemouth-bass');
      expect(result).toEqual(mockTagResponse);
    });
  });

  describe('createTag', () => {
    it('should create a new tag', async () => {
      const createDto: CreateTagDto = {
        name: 'Largemouth Bass',
        description: 'A popular game fish',
        category: 'species',
        color: '#3B82F6',
      };

      tagsService.createTag.mockResolvedValue(mockTagResponse);

      const result = await controller.createTag(createDto, { user: mockUser });

      expect(tagsService.createTag).toHaveBeenCalledWith(createDto, 'user-1');
      expect(result).toEqual(mockTagResponse);
    });
  });

  describe('createMultipleTags', () => {
    it('should create multiple tags', async () => {
      const createDto: CreateMultipleTagsDto = {
        names: ['Largemouth Bass', 'Smallmouth Bass'],
        category: 'species',
      };

      tagsService.createMultipleTags.mockResolvedValue([mockTagResponse]);

      const result = await controller.createMultipleTags(createDto, { user: mockUser });

      expect(tagsService.createMultipleTags).toHaveBeenCalledWith(createDto, 'user-1');
      expect(result).toEqual([mockTagResponse]);
    });
  });

  describe('updateTag', () => {
    it('should update a tag', async () => {
      const updateDto: UpdateTagDto = {
        description: 'Updated description',
        category: 'fish',
      };

      const updatedTag = { ...mockTagResponse, ...updateDto };
      tagsService.updateTag.mockResolvedValue(updatedTag);

      const result = await controller.updateTag('largemouth-bass', updateDto, { user: mockUser });

      expect(tagsService.updateTag).toHaveBeenCalledWith('largemouth-bass', updateDto, 'user-1');
      expect(result).toEqual(updatedTag);
    });
  });

  describe('moderateTag', () => {
    it('should moderate a tag', async () => {
      const moderateDto: ModerateTagDto = {
        status: TagStatus.Approved,
        moderationNote: 'Approved by moderator',
      };

      const moderatedTag = {
        ...mockTagResponse,
        status: TagStatus.Approved,
        moderatedBy: {
          id: 'moderator-1',
          username: 'moderator',
          displayName: 'Moderator',
        },
        moderatedAt: new Date(),
      };

      tagsService.moderateTag.mockResolvedValue(moderatedTag);

      const result = await controller.moderateTag('largemouth-bass', moderateDto, { user: mockUser });

      expect(tagsService.moderateTag).toHaveBeenCalledWith('largemouth-bass', moderateDto, 'user-1');
      expect(result).toEqual(moderatedTag);
    });
  });

  describe('bulkModerate', () => {
    it('should bulk moderate tags', async () => {
      const bulkDto: BulkTagOperationDto = {
        tagNames: ['tag1', 'tag2'],
        status: TagStatus.Approved,
        moderationNote: 'Bulk approval',
      };

      const mockResponse: BulkTagOperationResponseDto = {
        processed: 2,
        successful: 2,
        failed: 0,
        errors: [],
        message: 'Successfully moderated 2 out of 2 tags',
      };

      tagsService.bulkModerate.mockResolvedValue(mockResponse);

      const result = await controller.bulkModerate(bulkDto, { user: mockUser });

      expect(tagsService.bulkModerate).toHaveBeenCalledWith(bulkDto, 'user-1');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('validation', () => {
    it('should validate CreateTagDto', () => {
      const dto = new CreateTagDto();
      dto.name = 'Test Tag';
      dto.description = 'Test description';
      dto.category = 'test';
      dto.color = '#FF0000';

      expect(dto.name).toBe('Test Tag');
      expect(dto.description).toBe('Test description');
      expect(dto.category).toBe('test');
      expect(dto.color).toBe('#FF0000');
    });

    it('should validate TagSearchQueryDto', () => {
      const dto = new TagSearchQueryDto();
      dto.search = 'bass';
      dto.category = 'species';
      dto.status = TagStatus.Approved;
      dto.limit = 20;
      dto.offset = 0;
      dto.sortBy = 'usageCount';
      dto.sortOrder = 'DESC';

      expect(dto.search).toBe('bass');
      expect(dto.category).toBe('species');
      expect(dto.status).toBe(TagStatus.Approved);
      expect(dto.limit).toBe(20);
      expect(dto.offset).toBe(0);
      expect(dto.sortBy).toBe('usageCount');
      expect(dto.sortOrder).toBe('DESC');
    });

    it('should validate ModerateTagDto', () => {
      const dto = new ModerateTagDto();
      dto.status = TagStatus.Approved;
      dto.moderationNote = 'Approved';

      expect(dto.status).toBe(TagStatus.Approved);
      expect(dto.moderationNote).toBe('Approved');
    });
  });
});
