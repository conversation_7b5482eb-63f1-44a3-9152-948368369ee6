import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TagEntity } from '@/database/entities/tag.entity';
import { TagRepository } from '@/database/repositories/tag.repository';
import { TagNormalizationService } from '@/services/tag-normalization.service';
import { TagsController } from './tags.controller';
import { TagsService } from './tags.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([TagEntity]),
  ],
  controllers: [TagsController],
  providers: [
    TagsService,
    TagRepository,
    TagNormalizationService,
  ],
  exports: [
    TagsService,
    TagRepository,
    TagNormalizationService,
  ],
})
export class TagsModule {}
