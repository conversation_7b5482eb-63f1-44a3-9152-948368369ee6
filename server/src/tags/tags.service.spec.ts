import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common';
import { TagsService } from './tags.service';
import { TagRepository } from '@/database/repositories/tag.repository';
import { TagNormalizationService } from '@/services/tag-normalization.service';
import { Tag, TagStatus, User } from '@/types/global';
import { CreateTagDto, UpdateTagDto, ModerateTagDto } from './dto/tags.dto';

describe('TagsService', () => {
  let service: TagsService;
  let tagRepository: jest.Mocked<TagRepository>;
  let tagNormalizationService: jest.Mocked<TagNormalizationService>;

  const mockUser: User = {
    id: 'user-1',
    uname: 'testuser',
    email: '<EMAIL>',
    emailVerified: true,
    status: 'Active' as any,
    profile: { displayName: 'Test User' } as any,
    prefs: {} as any,
    activity: {} as any,
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: null,
    lastLoginAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockTag: Tag = {
    name: 'largemouth-bass',
    displayName: 'Largemouth Bass',
    description: 'A popular game fish',
    category: 'species',
    color: '#3B82F6',
    status: TagStatus.Pending,
    usageCount: 0,
    createdBy: mockUser,
    moderatedBy: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    moderatedAt: null,
  };

  beforeEach(async () => {
    const mockTagRepository = {
      createTag: jest.fn(),
      findTagByName: jest.fn(),
      findTagsByNames: jest.fn(),
      updateTag: jest.fn(),
      deleteTag: jest.fn(),
      searchTags: jest.fn(),
      moderateTag: jest.fn(),
      getTagStats: jest.fn(),
      incrementUsageCount: jest.fn(),
      getPopularTags: jest.fn(),
    };

    const mockTagNormalizationService = {
      normalizeTagName: jest.fn(),
      normalizeTagNames: jest.fn(),
      createDisplayName: jest.fn(),
      isValidNormalizedTag: jest.fn(),
      extractTagsFromText: jest.fn(),
      suggestSimilarTags: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TagsService,
        {
          provide: TagRepository,
          useValue: mockTagRepository,
        },
        {
          provide: TagNormalizationService,
          useValue: mockTagNormalizationService,
        },
      ],
    }).compile();

    service = module.get<TagsService>(TagsService);
    tagRepository = module.get(TagRepository);
    tagNormalizationService = module.get(TagNormalizationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createTag', () => {
    const createDto: CreateTagDto = {
      name: 'Largemouth Bass',
      description: 'A popular game fish',
      category: 'species',
      color: '#3B82F6',
    };

    it('should create a new tag successfully', async () => {
      tagNormalizationService.normalizeTagName.mockReturnValue('largemouth-bass');
      tagRepository.findTagByName.mockResolvedValue(null);
      tagRepository.createTag.mockResolvedValue(mockTag);

      const result = await service.createTag(createDto, 'user-1');

      expect(tagNormalizationService.normalizeTagName).toHaveBeenCalledWith('Largemouth Bass');
      expect(tagRepository.findTagByName).toHaveBeenCalledWith('largemouth-bass');
      expect(tagRepository.createTag).toHaveBeenCalledWith({
        name: 'largemouth-bass',
        displayName: 'Largemouth Bass',
        description: 'A popular game fish',
        category: 'species',
        color: '#3B82F6',
        status: TagStatus.Pending,
        usageCount: 0,
        createdBy: 'user-1',
      });
      expect(result.name).toBe('largemouth-bass');
    });

    it('should throw ConflictException if tag already exists', async () => {
      tagNormalizationService.normalizeTagName.mockReturnValue('largemouth-bass');
      tagRepository.findTagByName.mockResolvedValue(mockTag);

      await expect(service.createTag(createDto, 'user-1')).rejects.toThrow(ConflictException);
    });
  });

  describe('createMultipleTags', () => {
    const createDto = {
      names: ['Largemouth Bass', 'Smallmouth Bass', 'Largemouth Bass'], // Duplicate
      category: 'species',
    };

    it('should create multiple tags and handle duplicates', async () => {
      tagNormalizationService.normalizeTagNames.mockReturnValue(['largemouth-bass', 'smallmouth-bass']);
      tagRepository.findTagByName
        .mockResolvedValueOnce(null) // largemouth-bass doesn't exist
        .mockResolvedValueOnce(mockTag); // smallmouth-bass exists
      tagRepository.createTag.mockResolvedValue(mockTag);

      const result = await service.createMultipleTags(createDto, 'user-1');

      expect(tagNormalizationService.normalizeTagNames).toHaveBeenCalledWith(['Largemouth Bass', 'Smallmouth Bass', 'Largemouth Bass']);
      expect(result).toHaveLength(2);
    });
  });

  describe('getOrCreateTags', () => {
    it('should get existing tags and create new ones', async () => {
      const tagNames = ['Largemouth Bass', 'New Tag'];
      tagNormalizationService.normalizeTagNames.mockReturnValue(['largemouth-bass', 'new-tag']);
      tagRepository.findTagByName
        .mockResolvedValueOnce(mockTag) // largemouth-bass exists
        .mockResolvedValueOnce(null); // new-tag doesn't exist
      tagRepository.createTag.mockResolvedValue({
        ...mockTag,
        name: 'new-tag',
        displayName: 'New Tag',
      });

      const result = await service.getOrCreateTags(tagNames, 'user-1');

      expect(result).toHaveLength(2);
      expect(tagRepository.createTag).toHaveBeenCalledTimes(1);
    });
  });

  describe('getTag', () => {
    it('should return tag if found', async () => {
      tagNormalizationService.normalizeTagName.mockReturnValue('largemouth-bass');
      tagRepository.findTagByName.mockResolvedValue(mockTag);

      const result = await service.getTag('Largemouth Bass');

      expect(result.name).toBe('largemouth-bass');
    });

    it('should throw NotFoundException if tag not found', async () => {
      tagNormalizationService.normalizeTagName.mockReturnValue('nonexistent-tag');
      tagRepository.findTagByName.mockResolvedValue(null);

      await expect(service.getTag('Nonexistent Tag')).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateTag', () => {
    const updateDto: UpdateTagDto = {
      description: 'Updated description',
      category: 'fish',
    };

    it('should update tag if user is creator', async () => {
      tagNormalizationService.normalizeTagName.mockReturnValue('largemouth-bass');
      tagRepository.findTagByName.mockResolvedValue(mockTag);
      tagRepository.updateTag.mockResolvedValue({ ...mockTag, ...updateDto });

      const result = await service.updateTag('largemouth-bass', updateDto, 'user-1');

      expect(tagRepository.updateTag).toHaveBeenCalledWith('largemouth-bass', updateDto);
      expect(result.description).toBe('Updated description');
    });

    it('should throw ForbiddenException if user is not creator', async () => {
      tagNormalizationService.normalizeTagName.mockReturnValue('largemouth-bass');
      tagRepository.findTagByName.mockResolvedValue(mockTag);

      await expect(service.updateTag('largemouth-bass', updateDto, 'other-user')).rejects.toThrow(ForbiddenException);
    });
  });

  describe('moderateTag', () => {
    const moderateDto: ModerateTagDto = {
      status: TagStatus.Approved,
      moderationNote: 'Approved by moderator',
    };

    it('should moderate tag successfully', async () => {
      tagNormalizationService.normalizeTagName.mockReturnValue('largemouth-bass');
      tagRepository.findTagByName.mockResolvedValue(mockTag);
      tagRepository.moderateTag.mockResolvedValue({
        ...mockTag,
        status: TagStatus.Approved,
        moderatedBy: mockUser,
        moderatedAt: new Date(),
      });

      const result = await service.moderateTag('largemouth-bass', moderateDto, 'moderator-1');

      expect(tagRepository.moderateTag).toHaveBeenCalledWith('largemouth-bass', TagStatus.Approved, 'moderator-1');
      expect(result.status).toBe(TagStatus.Approved);
    });
  });

  describe('searchTags', () => {
    it('should search tags with query parameters', async () => {
      const query = {
        search: 'bass',
        category: 'species',
        status: TagStatus.Approved,
        limit: 10,
        offset: 0,
      };

      tagRepository.searchTags.mockResolvedValue({
        tags: [mockTag],
        total: 1,
      });

      const result = await service.searchTags(query);

      expect(tagRepository.searchTags).toHaveBeenCalledWith(query);
      expect(result.tags).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(result.hasMore).toBe(false);
    });
  });

  describe('getTagStats', () => {
    it('should return tag statistics', async () => {
      const mockStats = {
        totalTags: 100,
        approvedTags: 80,
        pendingTags: 15,
        rejectedTags: 5,
        categoryCounts: { species: 50, technique: 30 },
      };

      tagRepository.getTagStats.mockResolvedValue(mockStats);
      tagRepository.searchTags.mockResolvedValue({
        tags: [mockTag],
        total: 1,
      });

      const result = await service.getTagStats();

      expect(result.totalTags).toBe(100);
      expect(result.recentTags).toHaveLength(1);
    });
  });

  describe('bulkModerate', () => {
    it('should moderate multiple tags', async () => {
      const bulkDto = {
        tagNames: ['tag1', 'tag2', 'nonexistent'],
        status: TagStatus.Approved,
        moderationNote: 'Bulk approval',
      };

      tagRepository.moderateTag
        .mockResolvedValueOnce(mockTag) // tag1 success
        .mockResolvedValueOnce(mockTag) // tag2 success
        .mockRejectedValueOnce(new Error('Tag not found')); // nonexistent fails

      const result = await service.bulkModerate(bulkDto, 'moderator-1');

      expect(result.processed).toBe(3);
      expect(result.successful).toBe(2);
      expect(result.failed).toBe(1);
      expect(result.errors).toHaveLength(1);
    });
  });

  describe('incrementTagUsage', () => {
    it('should increment usage count for tags', async () => {
      tagNormalizationService.normalizeTagNames.mockReturnValue(['tag1', 'tag2']);

      await service.incrementTagUsage(['Tag1', 'Tag2']);

      expect(tagRepository.incrementUsageCount).toHaveBeenCalledWith('tag1');
      expect(tagRepository.incrementUsageCount).toHaveBeenCalledWith('tag2');
    });

    it('should not throw error if increment fails', async () => {
      tagNormalizationService.normalizeTagNames.mockReturnValue(['tag1']);
      tagRepository.incrementUsageCount.mockRejectedValue(new Error('Database error'));

      // Should not throw
      await expect(service.incrementTagUsage(['Tag1'])).resolves.toBeUndefined();
    });
  });
});
