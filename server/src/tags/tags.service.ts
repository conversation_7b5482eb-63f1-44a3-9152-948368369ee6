import { Injectable, Logger, NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common';
import { TagRepository } from '@/database/repositories/tag.repository';
import { TagNormalizationService } from '@/services/tag-normalization.service';
import { Tag, TagStatus, User } from '@/types/global';
import {
  CreateTagDto,
  CreateMultipleTagsDto,
  UpdateTagDto,
  ModerateTagDto,
  TagSearchQueryDto,
  TagResponseDto,
  TagListResponseDto,
  TagStatsResponseDto,
  TagSuggestionsResponseDto,
  BulkTagOperationDto,
  BulkTagOperationResponseDto,
} from './dto/tags.dto';

@Injectable()
export class TagsService {
  private readonly logger = new Logger(TagsService.name);

  constructor(
    private readonly tagRepository: TagRepository,
    private readonly tagNormalizationService: TagNormalizationService,
  ) { }

  // Convert domain type to response DTO
  private tagToResponseDto (tag: Tag): TagResponseDto {
    return {
      name: tag.name,
      displayName: tag.displayName,
      description: tag.description,
      category: tag.category,
      color: tag.color,
      status: tag.status,
      usageCount: tag.usageCount,
      createdBy: {
        id: tag.createdBy.id,
        username: tag.createdBy.uname,
        displayName: tag.createdBy.profile?.displayName || tag.createdBy.uname,
      },
      moderatedBy: tag.moderatedBy ? {
        id: tag.moderatedBy.id,
        username: tag.moderatedBy.uname,
        displayName: tag.moderatedBy.profile?.displayName || tag.moderatedBy.uname,
      } : undefined,
      createdAt: tag.createdAt,
      updatedAt: tag.updatedAt,
      moderatedAt: tag.moderatedAt,
    };
  }

  // Create a single tag
  async createTag (createDto: CreateTagDto, userId: string): Promise<TagResponseDto> {
    try {
      const normalizedName = this.tagNormalizationService.normalizeTagName(createDto.name);
      const displayName = createDto.name.trim();

      // Check if tag already exists
      const existingTag = await this.tagRepository.findTagByName(normalizedName);
      if (existingTag) {
        throw new ConflictException(`Tag "${normalizedName}" already exists`);
      }

      const tag = await this.tagRepository.createTag({
        name: normalizedName,
        displayName,
        description: createDto.description,
        category: createDto.category,
        color: createDto.color || '#3B82F6',
        status: TagStatus.Pending,
        usageCount: 0,
        createdBy: userId,
      });

      this.logger.log(`Created tag: ${normalizedName} by user ${userId}`);
      return this.tagToResponseDto(tag);
    } catch (error) {
      this.logger.error(`Failed to create tag: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Create multiple tags (for bulk operations)
  async createMultipleTags (createDto: CreateMultipleTagsDto, userId: string): Promise<TagResponseDto[]> {
    try {
      const normalizedNames = this.tagNormalizationService.normalizeTagNames(createDto.names);
      const results: TagResponseDto[] = [];

      for (const normalizedName of normalizedNames) {
        try {
          // Check if tag already exists
          const existingTag = await this.tagRepository.findTagByName(normalizedName);
          if (existingTag) {
            // If tag exists, just return it
            results.push(this.tagToResponseDto(existingTag));
            continue;
          }

          // Create display name from the original input
          const originalName = createDto.names.find(name =>
            this.tagNormalizationService.normalizeTagName(name) === normalizedName
          ) || normalizedName;

          const displayName = originalName.trim();

          const tag = await this.tagRepository.createTag({
            name: normalizedName,
            displayName,
            category: createDto.category,
            color: '#3B82F6',
            status: TagStatus.Pending,
            usageCount: 0,
            createdBy: userId,
          });

          results.push(this.tagToResponseDto(tag));
        } catch (error) {
          this.logger.warn(`Failed to create tag ${normalizedName}: ${error.message}`);
          // Continue with other tags
        }
      }

      this.logger.log(`Created ${results.length} tags by user ${userId}`);
      return results;
    } catch (error) {
      this.logger.error(`Failed to create multiple tags: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Get or create tags (used when creating posts)
  async getOrCreateTags (tagNames: string[], userId: string): Promise<TagResponseDto[]> {
    try {
      const normalizedNames = this.tagNormalizationService.normalizeTagNames(tagNames);
      const results: TagResponseDto[] = [];

      for (const normalizedName of normalizedNames) {
        // Try to find existing tag
        let tag = await this.tagRepository.findTagByName(normalizedName);

        if (!tag) {
          // Create new tag as pending
          const originalName = tagNames.find(name =>
            this.tagNormalizationService.normalizeTagName(name) === normalizedName
          ) || normalizedName;

          const displayName = originalName.trim();

          tag = await this.tagRepository.createTag({
            name: normalizedName,
            displayName,
            color: '#3B82F6',
            status: TagStatus.Pending,
            usageCount: 0,
            createdBy: userId,
          });

          this.logger.log(`Auto-created pending tag: ${normalizedName}`);
        }

        results.push(this.tagToResponseDto(tag));
      }

      return results;
    } catch (error) {
      this.logger.error(`Failed to get or create tags: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Get tag by name
  async getTag (name: string): Promise<TagResponseDto> {
    try {
      const normalizedName = this.tagNormalizationService.normalizeTagName(name);
      const tag = await this.tagRepository.findTagByName(normalizedName);

      if (!tag) {
        throw new NotFoundException(`Tag "${normalizedName}" not found`);
      }

      return this.tagToResponseDto(tag);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to get tag ${name}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Search tags
  async searchTags (query: TagSearchQueryDto): Promise<TagListResponseDto> {
    try {
      const { tags, total } = await this.tagRepository.searchTags(query);

      return {
        tags: tags.map(tag => this.tagToResponseDto(tag)),
        total,
        limit: query.limit || 20,
        offset: query.offset || 0,
        hasMore: (query.offset || 0) + (query.limit || 20) < total,
      };
    } catch (error) {
      this.logger.error(`Failed to search tags: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Update tag
  async updateTag (name: string, updateDto: UpdateTagDto, userId: string): Promise<TagResponseDto> {
    try {
      const normalizedName = this.tagNormalizationService.normalizeTagName(name);
      const tag = await this.tagRepository.findTagByName(normalizedName);

      if (!tag) {
        throw new NotFoundException(`Tag "${normalizedName}" not found`);
      }

      // Only creator or moderators can update tags
      if (tag.createdBy.id !== userId) {
        throw new ForbiddenException('Only the tag creator can update this tag');
      }

      const updatedTag = await this.tagRepository.updateTag(normalizedName, updateDto);

      this.logger.log(`Updated tag: ${normalizedName} by user ${userId}`);
      return this.tagToResponseDto(updatedTag);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }
      this.logger.error(`Failed to update tag ${name}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Moderate tag (approve/reject)
  async moderateTag (name: string, moderateDto: ModerateTagDto, moderatorId: string): Promise<TagResponseDto> {
    try {
      const normalizedName = this.tagNormalizationService.normalizeTagName(name);
      const tag = await this.tagRepository.findTagByName(normalizedName);

      if (!tag) {
        throw new NotFoundException(`Tag "${normalizedName}" not found`);
      }

      const moderatedTag = await this.tagRepository.moderateTag(
        normalizedName,
        moderateDto.status,
        moderatorId
      );

      this.logger.log(`Moderated tag: ${normalizedName} to ${moderateDto.status} by user ${moderatorId}`);
      return this.tagToResponseDto(moderatedTag);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to moderate tag ${name}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Get tag statistics
  async getTagStats (): Promise<TagStatsResponseDto> {
    try {
      const stats = await this.tagRepository.getTagStats();
      const recentTags = await this.tagRepository.searchTags({
        limit: 10,
        offset: 0,
        sortBy: 'createdAt',
        sortOrder: 'DESC',
      });

      return {
        ...stats,
        recentTags: recentTags.tags.map(tag => this.tagToResponseDto(tag)),
      };
    } catch (error) {
      this.logger.error(`Failed to get tag stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Get tag suggestions
  async getTagSuggestions (query: string, limit: number = 5): Promise<TagSuggestionsResponseDto> {
    try {
      // Get approved tags for suggestions
      const { tags } = await this.tagRepository.searchTags({
        status: TagStatus.Approved,
        search: query,
        limit: limit * 2, // Get more to filter
        sortBy: 'usageCount',
        sortOrder: 'DESC',
      });

      const suggestions = tags
        .map(tag => tag.name)
        .slice(0, limit);

      return {
        suggestions,
        query,
      };
    } catch (error) {
      this.logger.error(`Failed to get tag suggestions: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Bulk moderate tags
  async bulkModerate (bulkDto: BulkTagOperationDto, moderatorId: string): Promise<BulkTagOperationResponseDto> {
    try {
      const results = {
        processed: 0,
        successful: 0,
        failed: 0,
        errors: [] as Array<{ tagName: string; error: string }>,
      };

      for (const tagName of bulkDto.tagNames) {
        results.processed++;

        try {
          await this.tagRepository.moderateTag(tagName, bulkDto.status, moderatorId);
          results.successful++;
        } catch (error) {
          results.failed++;
          results.errors.push({
            tagName,
            error: error.message,
          });
        }
      }

      this.logger.log(`Bulk moderated ${results.successful}/${results.processed} tags by user ${moderatorId}`);

      return {
        ...results,
        message: `Successfully moderated ${results.successful} out of ${results.processed} tags`,
      };
    } catch (error) {
      this.logger.error(`Failed to bulk moderate tags: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Increment usage count when tag is used
  async incrementTagUsage (tagNames: string[]): Promise<void> {
    try {
      const normalizedNames = this.tagNormalizationService.normalizeTagNames(tagNames);

      for (const name of normalizedNames) {
        await this.tagRepository.incrementUsageCount(name);
      }
    } catch (error) {
      this.logger.warn(`Failed to increment tag usage: ${error.message}`);
      // Don't throw error to avoid breaking main operations
    }
  }
}
