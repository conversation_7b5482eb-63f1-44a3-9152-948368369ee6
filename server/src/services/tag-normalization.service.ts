import { Injectable, BadRequestException } from '@nestjs/common';

@Injectable()
export class TagNormalizationService {
  private readonly MAX_TAG_LENGTH = 50;
  private readonly MIN_TAG_LENGTH = 2;
  
  // Allowed characters: letters, numbers, parentheses, and hyphens (after normalization)
  private readonly ALLOWED_CHARS_REGEX = /^[a-z0-9()-]+$/;
  
  // Characters to remove during normalization
  private readonly INVALID_CHARS_REGEX = /[^a-zA-Z0-9\s()]/g;

  /**
   * Normalize a tag name for storage as primary key
   * - Convert to lowercase
   * - Replace spaces with hyphens
   * - Remove invalid characters
   * - Validate length and format
   */
  normalizeTagName(input: string): string {
    if (!input || typeof input !== 'string') {
      throw new BadRequestException('Tag name must be a non-empty string');
    }

    // Trim whitespace
    let normalized = input.trim();

    if (normalized.length === 0) {
      throw new BadRequestException('Tag name cannot be empty');
    }

    if (normalized.length > this.MAX_TAG_LENGTH) {
      throw new BadRequestException(`Tag name cannot exceed ${this.MAX_TAG_LENGTH} characters`);
    }

    // Remove invalid characters first
    normalized = normalized.replace(this.INVALID_CHARS_REGEX, '');

    // Convert to lowercase
    normalized = normalized.toLowerCase();

    // Replace multiple spaces with single space
    normalized = normalized.replace(/\s+/g, ' ');

    // Replace spaces with hyphens
    normalized = normalized.replace(/\s/g, '-');

    // Remove multiple consecutive hyphens
    normalized = normalized.replace(/-+/g, '-');

    // Remove leading/trailing hyphens
    normalized = normalized.replace(/^-+|-+$/g, '');

    // Validate final result
    if (normalized.length < this.MIN_TAG_LENGTH) {
      throw new BadRequestException(`Tag name must be at least ${this.MIN_TAG_LENGTH} characters after normalization`);
    }

    if (!this.ALLOWED_CHARS_REGEX.test(normalized)) {
      throw new BadRequestException('Tag name contains invalid characters. Only letters, numbers, and parentheses are allowed.');
    }

    // Check for reserved tag names
    if (this.isReservedTagName(normalized)) {
      throw new BadRequestException(`"${normalized}" is a reserved tag name`);
    }

    return normalized;
  }

  /**
   * Normalize multiple tag names and remove duplicates
   */
  normalizeTagNames(inputs: string[]): string[] {
    if (!Array.isArray(inputs)) {
      throw new BadRequestException('Tag names must be provided as an array');
    }

    const normalized = inputs.map(input => this.normalizeTagName(input));
    
    // Remove duplicates while preserving order
    return [...new Set(normalized)];
  }

  /**
   * Create display name from normalized tag name
   * Converts "largemouth-bass" to "Largemouth Bass"
   */
  createDisplayName(normalizedName: string): string {
    return normalizedName
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Validate if a string is a properly normalized tag name
   */
  isValidNormalizedTag(tagName: string): boolean {
    try {
      const normalized = this.normalizeTagName(tagName);
      return normalized === tagName;
    } catch {
      return false;
    }
  }

  /**
   * Check if a tag name is reserved (system tags, etc.)
   */
  private isReservedTagName(normalizedName: string): boolean {
    const reservedNames = [
      'admin',
      'moderator',
      'system',
      'deleted',
      'banned',
      'private',
      'public',
      'test',
      'null',
      'undefined',
      'true',
      'false',
    ];

    return reservedNames.includes(normalizedName);
  }

  /**
   * Extract and normalize tags from a text string
   * Useful for auto-tagging from post content
   */
  extractTagsFromText(text: string, maxTags: number = 10): string[] {
    if (!text || typeof text !== 'string') {
      return [];
    }

    // Simple extraction - look for hashtags or common fishing terms
    const hashtagRegex = /#([a-zA-Z0-9\s()]+)/g;
    const matches = text.match(hashtagRegex) || [];
    
    const extractedTags = matches
      .map(match => match.substring(1)) // Remove the #
      .slice(0, maxTags);

    try {
      return this.normalizeTagNames(extractedTags);
    } catch {
      // If normalization fails, return empty array
      return [];
    }
  }

  /**
   * Suggest similar tag names for typo correction
   * Simple implementation - could be enhanced with fuzzy matching
   */
  suggestSimilarTags(input: string, existingTags: string[]): string[] {
    const normalized = input.toLowerCase().replace(/\s/g, '-');
    
    return existingTags
      .filter(tag => {
        // Simple similarity check
        return tag.includes(normalized) || 
               normalized.includes(tag) ||
               this.levenshteinDistance(tag, normalized) <= 2;
      })
      .slice(0, 5); // Return top 5 suggestions
  }

  /**
   * Calculate Levenshtein distance for simple fuzzy matching
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }
}
