import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from './config/config.module';
import { DatabaseModule } from './database/database.module';
import { AuthModule } from './auth/auth.module';
import { SecurityModule } from './security/security.module';
import { UsersModule } from './users/users.module';
import { CommunitiesModule } from './communities/communities.module';
import { PostsModule } from './posts/posts.module';
import { CommentsModule } from './comments/comments.module';
import { FishingSpotsModule } from './fishing-spots/fishing-spots.module';
import { JwtAuthGuard } from './auth/guards/jwt.guard';
import { CsrfGuard } from './security/guards/csrf.guard';
import { AppConfigService } from './config/config.service';

@Module({
  imports: [
    ConfigModule,
    ThrottlerModule.forRootAsync({
      inject: [AppConfigService],
      useFactory: (configService: AppConfigService) => [
        {
          ttl: configService.throttleTtl * 1000, // Convert to milliseconds
          limit: configService.throttleLimit,
        },
      ],
    }),
    DatabaseModule,
    AuthModule,
    SecurityModule,
    UsersModule,
    CommunitiesModule,
    PostsModule,
    CommentsModule,
    FishingSpotsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: CsrfGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule { }
