// Base status type that can be extended
export type StatusBase = 'Pending' | 'Active' | 'Deleted';

// Enums for database entities
export enum UserStatus {
  Active = 'Active',
  Inactive = 'Inactive',
  Banned = 'Banned',
  Deleted = 'Deleted',
}

export enum PostStatus {
  Draft = 'Draft',
  Published = 'Published',
  Archived = 'Archived',
  Deleted = 'Deleted',
  Flagged = 'Flagged',
  UnderReview = 'UnderReview',
}

export enum CommentStatus {
  Published = 'Published',
  Deleted = 'Deleted',
  Flagged = 'Flagged',
  UnderReview = 'UnderReview',
}

export enum FishingSpotStatus {
  Active = 'Active',
  Inactive = 'Inactive',
  Private = 'Private',
  Closed = 'Closed',
}

export enum CommunityVisibility {
  Public = 'Public',
  Private = 'Private',
  Restricted = 'Restricted',
}

export enum InvitePermission {
  Anyone = 'Anyone',
  Members = 'Members',
  Moderators = 'Moderators',
  Admins = 'Admins',
}

export enum PostModeration {
  None = 'None',
  PreModeration = 'PreModeration',
  PostModeration = 'PostModeration',
}

export enum MemberRole {
  Member = 'Member',
  Moderator = 'Moderator',
  Admin = 'Admin',
}

export enum VoteType {
  Upvote = 'Upvote',
  Downvote = 'Downvote',
}

export enum FishingSpotType {
  Lake = 'Lake',
  River = 'River',
  Ocean = 'Ocean',
  Pond = 'Pond',
  Stream = 'Stream',
  Bay = 'Bay',
  Reservoir = 'Reservoir',
  Other = 'Other',
}

export enum AuthProvider {
  Google = 'google',
  Facebook = 'facebook',
  GitHub = 'github',
  Apple = 'apple',
}

// Legacy types for compatibility
export type GuideStatus = StatusBase | 'Verified';
export type VendorStatus = StatusBase | 'Verified';
export type ModerationType = 'Approved' | 'Rejected' | 'Deleted';

export type ModerationLog = {
  id: string;
  message: string;
  createdBy: User;
  createdAt: Date;
};

export type Moderation = {
  moderatedBy: User;
  moderatedAt: Date;
  action: ModerationType;
  logs: ModerationLog[];
};

export type UserProfile = {
  displayName: string;
  bio: string;
  location: string;
  website: string;
  pic: string;
  banner: string;
  isGuide: boolean;
  isVendor: boolean;
  // Social profile links
  socialLinks?: {
    twitter?: string;
    instagram?: string;
    youtube?: string;
    tiktok?: string;
  };
};

export type User = {
  id: string;
  uname: string;
  status: UserStatus;
  email: string;
  emailVerified: boolean;
  profile: UserProfile;
  prefs: UserPreferences;
  activity: UserActivity;
  savedContent: SavedContent[];
  subscribedCommunities: Community[];
  // Store only provider IDs, not tokens
  providerAccounts: {
    google?: string;
    facebook?: string;
    github?: string;
    apple?: string;
  };
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
};

// Auth-related types

export type AuthToken = {
  id: string;
  userId: string;
  provider: AuthProvider;
  providerUserId: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
};

export type Session = {
  id: string;
  userId: string;
  token: string;
  ipAddress?: string;
  userAgent?: string;
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
};

export type Community = {
  id: string;
  name: string;
  description: string;
  rules: string;
  visibility: CommunityVisibility;
  invitePermission: InvitePermission;
  postModeration: PostModeration;
  commentModeration: PostModeration;
  pic: string;
  banner: string;
  owner: User;
  createdAt: Date;
  updatedAt: Date;
};

export type CommunityMember = {
  id: string;
  user: User;
  community: Community;
  role: MemberRole;
  joinedAt: Date;
  updatedAt: Date;
};

export type CommunityInvite = {
  id: string;
  community: Community;
  invitedBy: User;
  invitedUser: User;
  role: MemberRole;
  status: 'Pending' | 'Accepted' | 'Rejected';
  createdAt: Date;
  updatedAt: Date;
};

export type Post = {
  id: string;
  title: string;
  status: PostStatus;
  content?: string;
  author: User;
  community: Community;
  fishingSpot?: FishingSpot;
  comments: Comment[];
  tags: Tag[];
  moderation?: Moderation;
  upvotes: number;
  downvotes: number;
  views: number;
  createdAt: Date;
  updatedAt: Date;
};

export type Comment = {
  id: string;
  content: string;
  status: CommentStatus;
  author: User;
  post: Post;
  parentComment?: string; // ID reference to parent comment
  moderation?: Moderation;
  upvotes: number;
  downvotes: number;
  createdAt: Date;
  updatedAt: Date;
};

export type Vote = {
  id: string;
  user: User;
  post?: Post;
  commentId?: string; // ID reference to comment within a post
  value: 1 | -1;
  createdAt: Date;
  updatedAt: Date;
};

export type FishingSpot = {
  id: string;
  name: string;
  status: FishingSpotStatus;
  spotType: FishingSpotType;
  isPublic: boolean;
  description: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    zipCode?: string;
  };
  conditions?: {
    waterType: string;
    depth?: string;
    structure?: string[];
    fishSpecies?: string[];
    bestTimes?: string[];
    seasonality?: string;
  };
  access?: {
    isPublic: boolean;
    requiresPermission?: boolean;
    fees?: string;
    restrictions?: string[];
    amenities?: string[];
  };
  typesOfFish: string[];
  rating: number;
  reviewCount: number;
  createdBy: User;
  moderation?: Moderation;
  createdAt: Date;
  updatedAt: Date;
};

export type Guide = {
  id: string;
  owner: User;
  name: string;
  status: GuideStatus;
  description: string;
  location: string;
  email: string;
  website: string;
  pic: string;
  banner: string;
  moderation?: Moderation;
  createdAt: Date;
  updatedAt: Date;
};

export type Vendor = {
  id: string;
  owner: User;
  name: string;
  status: VendorStatus;
  description: string;
  location: string;
  email: string;
  website: string;
  pic: string;
  banner: string;
  moderation?: Moderation;
  createdAt: Date;
  updatedAt: Date;
};

export type Pic = {
  id: string;
  url: string;
  post: Post;
  name?: string;
  description?: string;
  spot?: FishingSpot;
  community: Community;
  owner: User;
  views: number;
  createdAt: Date;
  updatedAt: Date;
};

// Notification system
export type NotificationType =
  | 'Mention'
  | 'Reply'
  | 'CommunityInvite'
  | 'PostModerated'
  | 'CommentModerated'
  | 'NewFollower'
  | 'UpvoteMilestone'
  | 'NewLogin';

export type NotificationPreference = {
  type: NotificationType;
  email: boolean;
  push: boolean;
  inApp: boolean;
};

export type Notification = {
  id: string;
  user: User;
  type: NotificationType;
  read: boolean;
  data: Record<string, any>;
  createdAt: Date;
};

// Content reporting
export type ReportReason =
  | 'Spam'
  | 'Harassment'
  | 'Violence'
  | 'Misinformation'
  | 'IllegalContent'
  | 'Other';

export type ReportStatus = 'Pending' | 'Reviewed' | 'Actioned' | 'Dismissed';

export type Report = {
  id: string;
  reason: ReportReason;
  description: string;
  reportedBy: User;
  post?: Post;
  fishingSpot?: FishingSpot;
  guide?: Guide;
  vendor?: Vendor;
  user?: User;
  status: ReportStatus;
  reviewedBy?: User;
  reviewedAt?: Date;
  createdAt: Date;
};

// User preferences embedded in User
export type UserPreferences = {
  theme: 'Light' | 'Dark' | 'System';
  notificationPreferences: NotificationPreference[];
  emailDigest: 'Daily' | 'Weekly' | 'Never';
  contentVisibility: 'Public' | 'Private';
  // Auth-related preferences
  twoFactorEnabled: boolean;
  sessionTimeout: 'Short' | 'Medium' | 'Long';
};

// User activity tracking embedded in User
export type UserActivity = {
  postsToday: number;
  commentsToday: number;
  votesToday: number;
  lastActivityAt: Date;
};

// Saved content embedded in User
export type SavedContent = {
  id: string;
  post?: Post;
  community?: Community;
  createdAt: Date;
};

// Analytics tracking
export type PostAnalytics = {
  id: string;
  post: Post;
  views: number;
  uniqueViewers: number;
  clickThroughRate: number;
  updatedAt: Date;
};

export type CommunityAnalytics = {
  id: string;
  community: Community;
  memberCount: number;
  activeMembers: number;
  postsLastWeek: number;
  commentsLastWeek: number;
  viewsLastWeek: number;
  updatedAt: Date;
};

// Content tagging
export type Tag = {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
};

// User following system
export type UserFollow = {
  id: string;
  follower: User;
  following: User;
  createdAt: Date;
};

// Community subscription (for users who want to follow but not join)
export type CommunitySubscription = {
  id: string;
  user: User;
  community: Community;
  createdAt: Date;
};

export type Device = {
  id: string;
  userId: string;
  name: string;
  type: 'Mobile' | 'Tablet' | 'Desktop' | 'Other';
  os: string;
  browser?: string;
  appVersion?: string;
  lastUsedAt: Date;
  createdAt: Date;
};

export type AuditLogAction =
  | 'Login'
  | 'Logout'
  | 'PasswordChange'
  | 'ProfileUpdate'
  | 'RoleChange'
  | 'AccountCreation'
  | 'AccountDeletion'
  | 'TwoFactorEnabled'
  | 'TwoFactorDisabled';

export type AuditLog = {
  id: string;
  userId: string;
  action: AuditLogAction;
  ipAddress?: string;
  userAgent?: string;
  details?: Record<string, any>;
  createdAt: Date;
};

export type RateLimitEntry = {
  id: string;
  key: string; // IP, user ID, or other identifier
  endpoint: string;
  count: number;
  resetAt: Date;
  createdAt: Date;
  updatedAt: Date;
};
