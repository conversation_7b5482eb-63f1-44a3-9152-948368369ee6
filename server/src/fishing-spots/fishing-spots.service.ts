import { Injectable, Logger, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { FishingSpotRepository } from '@/database/repositories/fishing-spot.repository';
import { UserRepository } from '@/database/repositories/user.repository';
import {
  CreateFishingSpotDto,
  UpdateFishingSpotDto,
  FishingSpotSearchQueryDto,
  NearbyFishingSpotsQueryDto,
  PaginationQueryDto,
  FishingSpotResponseDto,
  FishingSpotListResponseDto,
  UserResponseDto,
  ReportFishingSpotDto,
} from './dto/fishing-spots.dto';
import { FishingSpot, User, FishingSpotStatus } from '@/types/global';

@Injectable()
export class FishingSpotsService {
  private readonly logger = new Logger(FishingSpotsService.name);

  constructor(
    private readonly fishingSpotRepository: FishingSpotRepository,
    private readonly userRepository: UserRepository,
  ) { }

  // Helper method to convert FishingSpot to response DTO
  private fishingSpotToResponse (spot: FishingSpot): FishingSpotResponseDto {
    return {
      id: spot.id,
      name: spot.name,
      description: spot.description,
      spotType: spot.spotType,
      status: spot.status,
      location: spot.location || {
        latitude: spot.coordinates.lat,
        longitude: spot.coordinates.lng,
      },
      conditions: spot.conditions,
      access: spot.access,
      rating: spot.rating,
      reviewCount: spot.reviewCount,
      createdBy: this.userToResponse(spot.createdBy),
      createdAt: spot.createdAt,
      updatedAt: spot.updatedAt,
    };
  }

  private userToResponse (user: User): UserResponseDto {
    return {
      id: user.id,
      uname: user.uname,
      profile: user.profile || {},
    };
  }

  // Create a new fishing spot
  async createFishingSpot (createDto: CreateFishingSpotDto, userId: string): Promise<FishingSpotResponseDto> {
    try {
      // Verify user exists
      const user = await this.userRepository.findUserById(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Create fishing spot entity data
      const spotData = {
        name: createDto.name,
        description: createDto.description,
        spotType: createDto.spotType,
        status: FishingSpotStatus.Active,
        location: {
          latitude: createDto.location.latitude,
          longitude: createDto.location.longitude,
          address: createDto.location.address,
          city: createDto.location.city,
          state: createDto.location.state,
          country: createDto.location.country,
          zipCode: createDto.location.zipCode,
        },
        conditions: createDto.conditions,
        access: createDto.access ? {
          isPublic: createDto.access.isPublic,
          requiresPermission: createDto.access.requiresPermission || false,
          fees: createDto.access.fees,
          restrictions: createDto.access.restrictions,
          amenities: createDto.access.amenities,
        } : { isPublic: true, requiresPermission: false },
        createdById: userId,
        rating: 0,
        reviewCount: 0,
      };

      const createdSpot = await this.fishingSpotRepository.createFishingSpot(spotData);
      this.logger.log(`Created fishing spot: ${createdSpot.name} by user ${userId}`);

      return this.fishingSpotToResponse(createdSpot);
    } catch (error) {
      this.logger.error(`Failed to create fishing spot: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Get fishing spot by ID
  async getFishingSpotById (id: string, userId?: string): Promise<FishingSpotResponseDto> {
    try {
      const spot = await this.fishingSpotRepository.findFishingSpotById(id);
      if (!spot) {
        throw new NotFoundException('Fishing spot not found');
      }

      // Check if user can view this spot
      if (!spot.isPublic && (!userId || spot.createdBy.id !== userId)) {
        throw new ForbiddenException('Access denied to private fishing spot');
      }

      return this.fishingSpotToResponse(spot);
    } catch (error) {
      this.logger.error(`Failed to get fishing spot ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Update fishing spot
  async updateFishingSpot (
    id: string,
    updateDto: UpdateFishingSpotDto,
    userId: string
  ): Promise<FishingSpotResponseDto> {
    try {
      const existingSpot = await this.fishingSpotRepository.findFishingSpotById(id);
      if (!existingSpot) {
        throw new NotFoundException('Fishing spot not found');
      }

      // Check if user is the creator
      if (existingSpot.createdBy.id !== userId) {
        throw new ForbiddenException('Only the creator can update this fishing spot');
      }

      // Prepare update data
      const updateData: any = {};

      if (updateDto.name) updateData.name = updateDto.name;
      if (updateDto.description) updateData.description = updateDto.description;
      if (updateDto.spotType) updateData.spotType = updateDto.spotType;

      if (updateDto.location) {
        updateData.location = {
          latitude: updateDto.location.latitude,
          longitude: updateDto.location.longitude,
          address: updateDto.location.address,
          city: updateDto.location.city,
          state: updateDto.location.state,
          country: updateDto.location.country,
          zipCode: updateDto.location.zipCode,
        };
      }

      if (updateDto.conditions) {
        updateData.conditions = updateDto.conditions;
      }

      if (updateDto.access) {
        updateData.access = updateDto.access;
      }

      const updatedSpot = await this.fishingSpotRepository.updateFishingSpot(id, updateData);
      this.logger.log(`Updated fishing spot: ${id} by user ${userId}`);

      return this.fishingSpotToResponse(updatedSpot);
    } catch (error) {
      this.logger.error(`Failed to update fishing spot ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Delete fishing spot
  async deleteFishingSpot (id: string, userId: string): Promise<void> {
    try {
      const existingSpot = await this.fishingSpotRepository.findFishingSpotById(id);
      if (!existingSpot) {
        throw new NotFoundException('Fishing spot not found');
      }

      // Check if user is the creator
      if (existingSpot.createdBy.id !== userId) {
        throw new ForbiddenException('Only the creator can delete this fishing spot');
      }

      await this.fishingSpotRepository.deleteFishingSpot(id);
      this.logger.log(`Deleted fishing spot: ${id} by user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to delete fishing spot ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Get fishing spots with search and filtering
  async getFishingSpots (query: FishingSpotSearchQueryDto, userId?: string): Promise<FishingSpotListResponseDto> {
    try {
      const { page = 1, limit = 20, ...filters } = query;

      // If no user is provided, only show public spots
      if (!userId) {
        filters.isPublic = true;
        filters.status = FishingSpotStatus.Active;
      }

      const { spots, total } = await this.fishingSpotRepository.findFishingSpots(page, limit, filters);

      // Filter out private spots if user is not the creator
      const filteredSpots = spots.filter(spot => {
        if (spot.isPublic) return true;
        return userId && spot.createdBy.id === userId;
      });

      const totalPages = Math.ceil(total / limit);

      return {
        spots: filteredSpots.map(spot => this.fishingSpotToResponse(spot)),
        total: filteredSpots.length,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(`Failed to get fishing spots: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Get nearby fishing spots
  async getNearbyFishingSpots (query: NearbyFishingSpotsQueryDto): Promise<FishingSpotResponseDto[]> {
    try {
      const { latitude, longitude, radiusKm = 10, limit = 20 } = query;

      const spots = await this.fishingSpotRepository.findNearbyFishingSpots(
        latitude,
        longitude,
        radiusKm,
        limit
      );

      return spots.map(spot => this.fishingSpotToResponse(spot));
    } catch (error) {
      this.logger.error(`Failed to get nearby fishing spots: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Get fishing spots by creator
  async getFishingSpotsByCreator (
    creatorId: string,
    query: PaginationQueryDto,
    requestingUserId?: string
  ): Promise<FishingSpotListResponseDto> {
    try {
      const { page = 1, limit = 20 } = query;

      const { spots, total } = await this.fishingSpotRepository.findFishingSpotsByCreator(
        creatorId,
        page,
        limit
      );

      // Filter out private spots if requesting user is not the creator
      const filteredSpots = spots.filter(spot => {
        if (spot.isPublic) return true;
        return requestingUserId === creatorId;
      });

      const totalPages = Math.ceil(total / limit);

      return {
        spots: filteredSpots.map(spot => this.fishingSpotToResponse(spot)),
        total: filteredSpots.length,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(`Failed to get fishing spots by creator ${creatorId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Report fishing spot (placeholder for future moderation system)
  async reportFishingSpot (id: string, reportDto: ReportFishingSpotDto, userId: string): Promise<void> {
    try {
      const spot = await this.fishingSpotRepository.findFishingSpotById(id);
      if (!spot) {
        throw new NotFoundException('Fishing spot not found');
      }

      // TODO: Implement reporting system with moderation queue
      this.logger.log(`Fishing spot ${id} reported by user ${userId}: ${reportDto.reason}`);

      // For now, just log the report
      // In the future, this would create a moderation record
    } catch (error) {
      this.logger.error(`Failed to report fishing spot ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
