import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, ForbiddenException } from '@nestjs/common';
import { FishingSpotsService } from './fishing-spots.service';
import { FishingSpotRepository } from '@/database/repositories/fishing-spot.repository';
import { UserRepository } from '@/database/repositories/user.repository';
import { FishingSpotType, FishingSpotStatus, UserStatus } from '@/types/global';

describe('FishingSpotsService', () => {
  let service: FishingSpotsService;
  let fishingSpotRepository: jest.Mocked<FishingSpotRepository>;
  let userRepository: jest.Mocked<UserRepository>;

  const mockUser = {
    id: 'user-1',
    uname: 'testuser',
    status: UserStatus.Active,
    email: '<EMAIL>',
    emailVerified: true,
    profile: {
      displayName: 'Test User',
      bio: 'Test bio',
      location: 'Test City',
      website: 'https://test.com',
      pic: 'test.jpg',
      banner: 'banner.jpg',
      isGuide: false,
      isVendor: false,
    },
    prefs: {
      theme: 'Light' as const,
      notificationPreferences: [],
      emailDigest: 'Weekly' as const,
      contentVisibility: 'Public' as const,
      twoFactorEnabled: false,
      sessionTimeout: 'Medium' as const,
    },
    activity: {
      postsToday: 0,
      commentsToday: 0,
      votesToday: 0,
      lastActivityAt: new Date(),
    },
    savedContent: [],
    subscribedCommunities: [],
    providerAccounts: {},
    lastLoginAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockFishingSpot = {
    id: 'spot-1',
    name: 'Test Lake',
    description: 'A great fishing spot',
    spotType: FishingSpotType.Lake,
    status: FishingSpotStatus.Active,
    isPublic: true,
    coordinates: { lat: 40.7128, lng: -74.0060 },
    location: {
      latitude: 40.7128,
      longitude: -74.0060,
      city: 'New York',
      state: 'NY',
    },
    conditions: {
      waterType: 'Freshwater',
      fishSpecies: ['Bass', 'Pike'],
    },
    access: {
      isPublic: true,
      requiresPermission: false,
    },
    typesOfFish: ['Bass', 'Pike'],
    rating: 4.5,
    reviewCount: 10,
    createdBy: mockUser,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockFishingSpotRepository = {
      createFishingSpot: jest.fn(),
      findFishingSpotById: jest.fn(),
      updateFishingSpot: jest.fn(),
      deleteFishingSpot: jest.fn(),
      findFishingSpots: jest.fn(),
      findNearbyFishingSpots: jest.fn(),
      findFishingSpotsByCreator: jest.fn(),
    };

    const mockUserRepository = {
      findUserById: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FishingSpotsService,
        {
          provide: FishingSpotRepository,
          useValue: mockFishingSpotRepository,
        },
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<FishingSpotsService>(FishingSpotsService);
    fishingSpotRepository = module.get(FishingSpotRepository);
    userRepository = module.get(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createFishingSpot', () => {
    const createDto = {
      name: 'Test Lake',
      description: 'A great fishing spot',
      spotType: FishingSpotType.Lake,
      location: {
        latitude: 40.7128,
        longitude: -74.0060,
        city: 'New York',
        state: 'NY',
      },
      conditions: {
        waterType: 'Freshwater',
        fishSpecies: ['Bass', 'Pike'],
      },
      access: {
        isPublic: true,
      },
    };

    it('should create a fishing spot successfully', async () => {
      userRepository.findUserById.mockResolvedValue(mockUser);
      fishingSpotRepository.createFishingSpot.mockResolvedValue(mockFishingSpot);

      const result = await service.createFishingSpot(createDto, 'user-1');

      expect(userRepository.findUserById).toHaveBeenCalledWith('user-1');
      expect(fishingSpotRepository.createFishingSpot).toHaveBeenCalledWith(
        expect.objectContaining({
          name: createDto.name,
          description: createDto.description,
          spotType: createDto.spotType,
          status: FishingSpotStatus.Active,
          createdById: 'user-1',
        })
      );
      expect(result.id).toBe(mockFishingSpot.id);
      expect(result.name).toBe(mockFishingSpot.name);
    });

    it('should throw NotFoundException if user does not exist', async () => {
      userRepository.findUserById.mockResolvedValue(null);

      await expect(service.createFishingSpot(createDto, 'user-1')).rejects.toThrow(
        NotFoundException
      );
    });
  });

  describe('getFishingSpotById', () => {
    it('should return a public fishing spot', async () => {
      fishingSpotRepository.findFishingSpotById.mockResolvedValue(mockFishingSpot);

      const result = await service.getFishingSpotById('spot-1');

      expect(fishingSpotRepository.findFishingSpotById).toHaveBeenCalledWith('spot-1');
      expect(result.id).toBe(mockFishingSpot.id);
    });

    it('should throw NotFoundException if spot does not exist', async () => {
      fishingSpotRepository.findFishingSpotById.mockResolvedValue(null);

      await expect(service.getFishingSpotById('spot-1')).rejects.toThrow(
        NotFoundException
      );
    });

    it('should throw ForbiddenException for private spot when user is not creator', async () => {
      const privateSpot = { ...mockFishingSpot, isPublic: false };
      fishingSpotRepository.findFishingSpotById.mockResolvedValue(privateSpot);

      await expect(service.getFishingSpotById('spot-1', 'other-user')).rejects.toThrow(
        ForbiddenException
      );
    });

    it('should allow creator to view private spot', async () => {
      const privateSpot = { ...mockFishingSpot, isPublic: false };
      fishingSpotRepository.findFishingSpotById.mockResolvedValue(privateSpot);

      const result = await service.getFishingSpotById('spot-1', 'user-1');

      expect(result.id).toBe(privateSpot.id);
    });
  });

  describe('updateFishingSpot', () => {
    const updateDto = {
      name: 'Updated Lake Name',
      description: 'Updated description',
    };

    it('should update fishing spot successfully', async () => {
      const updatedSpot = { ...mockFishingSpot, ...updateDto };
      fishingSpotRepository.findFishingSpotById.mockResolvedValue(mockFishingSpot);
      fishingSpotRepository.updateFishingSpot.mockResolvedValue(updatedSpot);

      const result = await service.updateFishingSpot('spot-1', updateDto, 'user-1');

      expect(fishingSpotRepository.findFishingSpotById).toHaveBeenCalledWith('spot-1');
      expect(fishingSpotRepository.updateFishingSpot).toHaveBeenCalledWith(
        'spot-1',
        expect.objectContaining(updateDto)
      );
      expect(result.name).toBe(updateDto.name);
    });

    it('should throw NotFoundException if spot does not exist', async () => {
      fishingSpotRepository.findFishingSpotById.mockResolvedValue(null);

      await expect(service.updateFishingSpot('spot-1', updateDto, 'user-1')).rejects.toThrow(
        NotFoundException
      );
    });

    it('should throw ForbiddenException if user is not creator', async () => {
      fishingSpotRepository.findFishingSpotById.mockResolvedValue(mockFishingSpot);

      await expect(service.updateFishingSpot('spot-1', updateDto, 'other-user')).rejects.toThrow(
        ForbiddenException
      );
    });
  });

  describe('deleteFishingSpot', () => {
    it('should delete fishing spot successfully', async () => {
      fishingSpotRepository.findFishingSpotById.mockResolvedValue(mockFishingSpot);
      fishingSpotRepository.deleteFishingSpot.mockResolvedValue(undefined);

      await service.deleteFishingSpot('spot-1', 'user-1');

      expect(fishingSpotRepository.findFishingSpotById).toHaveBeenCalledWith('spot-1');
      expect(fishingSpotRepository.deleteFishingSpot).toHaveBeenCalledWith('spot-1');
    });

    it('should throw NotFoundException if spot does not exist', async () => {
      fishingSpotRepository.findFishingSpotById.mockResolvedValue(null);

      await expect(service.deleteFishingSpot('spot-1', 'user-1')).rejects.toThrow(
        NotFoundException
      );
    });

    it('should throw ForbiddenException if user is not creator', async () => {
      fishingSpotRepository.findFishingSpotById.mockResolvedValue(mockFishingSpot);

      await expect(service.deleteFishingSpot('spot-1', 'other-user')).rejects.toThrow(
        ForbiddenException
      );
    });
  });

  describe('getFishingSpots', () => {
    it('should return fishing spots with pagination', async () => {
      const mockSpots = [mockFishingSpot];
      fishingSpotRepository.findFishingSpots.mockResolvedValue({
        spots: mockSpots,
        total: 1,
      });

      const result = await service.getFishingSpots({ page: 1, limit: 20 });

      expect(fishingSpotRepository.findFishingSpots).toHaveBeenCalledWith(
        1,
        20,
        expect.objectContaining({ isPublic: true, status: FishingSpotStatus.Active })
      );
      expect(result.spots).toHaveLength(1);
      expect(result.total).toBe(1);
    });
  });

  describe('getFishingSpotsByCreator', () => {
    it('should return fishing spots by creator', async () => {
      const mockSpots = [mockFishingSpot];
      fishingSpotRepository.findFishingSpotsByCreator.mockResolvedValue({
        spots: mockSpots,
        total: 1,
      });

      const result = await service.getFishingSpotsByCreator('user-1', { page: 1, limit: 20 });

      expect(fishingSpotRepository.findFishingSpotsByCreator).toHaveBeenCalledWith(
        'user-1',
        1,
        20
      );
      expect(result.spots).toHaveLength(1);
      expect(result.total).toBe(1);
    });
  });

  describe('getNearbyFishingSpots', () => {
    it('should return nearby fishing spots', async () => {
      const mockSpots = [mockFishingSpot];
      fishingSpotRepository.findNearbyFishingSpots.mockResolvedValue(mockSpots);

      const result = await service.getNearbyFishingSpots({
        latitude: 40.7128,
        longitude: -74.0060,
        radiusKm: 10,
        limit: 20,
      });

      expect(fishingSpotRepository.findNearbyFishingSpots).toHaveBeenCalledWith(
        40.7128,
        -74.0060,
        10,
        20
      );
      expect(result).toHaveLength(1);
    });
  });

  describe('reportFishingSpot', () => {
    it('should report fishing spot successfully', async () => {
      fishingSpotRepository.findFishingSpotById.mockResolvedValue(mockFishingSpot);

      await service.reportFishingSpot(
        'spot-1',
        { reason: 'Inappropriate content' },
        'user-1'
      );

      expect(fishingSpotRepository.findFishingSpotById).toHaveBeenCalledWith('spot-1');
    });

    it('should throw NotFoundException if spot does not exist', async () => {
      fishingSpotRepository.findFishingSpotById.mockResolvedValue(null);

      await expect(
        service.reportFishingSpot('spot-1', { reason: 'Test' }, 'user-1')
      ).rejects.toThrow(NotFoundException);
    });
  });
});
