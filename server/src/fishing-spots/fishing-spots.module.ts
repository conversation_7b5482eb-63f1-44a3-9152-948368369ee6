import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FishingSpotsController, CreatorFishingSpotsController, MyFishingSpotsController } from './fishing-spots.controller';
import { FishingSpotsService } from './fishing-spots.service';
import { FishingSpotRepository } from '@/database/repositories/fishing-spot.repository';
import { UserRepository } from '@/database/repositories/user.repository';
import { SecurityModule } from '@/security/security.module';
import {
  FishingSpotEntity,
  UserEntity,
} from '@/database/entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      FishingSpotEntity,
      UserEntity,
    ]),
    SecurityModule,
  ],
  controllers: [
    FishingSpotsController,
    CreatorFishingSpotsController,
    MyFishingSpotsController,
  ],
  providers: [
    FishingSpotsService,
    FishingSpotRepository,
    UserRepository,
  ],
  exports: [
    FishingSpotsService,
    FishingSpotRepository,
  ],
})
export class FishingSpotsModule { }
