import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  FishingSpot,
  User,
  FishingSpotStatus,
  FishingSpotType,
} from '@/types/global';
import { FishingSpotEntity } from '../entities/fishing-spot.entity';
import { UserEntity } from '../entities/user.entity';

@Injectable()
export class FishingSpotRepository {
  private readonly logger = new Logger(FishingSpotRepository.name);

  constructor(
    @InjectRepository(FishingSpotEntity)
    private fishingSpotRepository: Repository<FishingSpotEntity>,
  ) { }

  // Helper method to convert entity to type
  private entityToFishingSpot (entity: FishingSpotEntity): FishingSpot {
    return {
      id: entity.id,
      name: entity.name,
      status: entity.status,
      spotType: entity.spotType,
      isPublic: entity.access?.isPublic || false,
      description: entity.description,
      coordinates: {
        lat: entity.location.latitude,
        lng: entity.location.longitude,
      },
      location: entity.location,
      conditions: entity.conditions,
      access: entity.access,
      typesOfFish: entity.conditions?.fishSpecies || [],
      rating: entity.rating,
      reviewCount: entity.reviewCount,
      createdBy: entity.createdBy ? this.entityToUser(entity.createdBy) : null,
      moderation: undefined, // FishingSpot moderation not implemented yet
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  private entityToUser (entity: UserEntity): User {
    return {
      id: entity.id,
      uname: entity.uname,
      status: entity.status,
      email: entity.email,
      emailVerified: entity.emailVerified,
      profile: entity.profile,
      prefs: entity.prefs,
      activity: entity.activity,
      savedContent: entity.savedContent,
      subscribedCommunities: [], // Will be populated when needed
      providerAccounts: entity.providerAccounts || {},
      lastLoginAt: entity.lastLoginAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  // Fishing spot CRUD operations
  async createFishingSpot (spotData: Partial<FishingSpotEntity>): Promise<FishingSpot> {
    try {
      const spot = this.fishingSpotRepository.create(spotData);
      const savedSpot = await this.fishingSpotRepository.save(spot);

      // Load the spot with creator relation
      const spotWithCreator = await this.fishingSpotRepository.findOne({
        where: { id: savedSpot.id },
        relations: ['createdBy'],
      });

      this.logger.log(`Created fishing spot: ${savedSpot.name} (${savedSpot.id})`);
      return this.entityToFishingSpot(spotWithCreator);
    } catch (error) {
      this.logger.error(`Failed to create fishing spot: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findFishingSpotById (id: string): Promise<FishingSpot | null> {
    try {
      const spot = await this.fishingSpotRepository.findOne({
        where: { id },
        relations: ['createdBy'],
      });

      return spot ? this.entityToFishingSpot(spot) : null;
    } catch (error) {
      this.logger.error(`Failed to find fishing spot by id ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateFishingSpot (id: string, spotData: Partial<FishingSpotEntity>): Promise<FishingSpot> {
    try {
      const updateData = {
        ...spotData,
        updatedAt: new Date(),
      };

      await this.fishingSpotRepository.update(id, updateData);

      const updatedSpot = await this.fishingSpotRepository.findOne({
        where: { id },
        relations: ['createdBy'],
      });

      if (!updatedSpot) {
        throw new Error('Fishing spot not found after update');
      }

      this.logger.log(`Updated fishing spot: ${id}`);
      return this.entityToFishingSpot(updatedSpot);
    } catch (error) {
      this.logger.error(`Failed to update fishing spot ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deleteFishingSpot (id: string): Promise<void> {
    try {
      await this.fishingSpotRepository.delete(id);
      this.logger.log(`Deleted fishing spot: ${id}`);
    } catch (error) {
      this.logger.error(`Failed to delete fishing spot ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Fishing spot listing and search
  async findFishingSpots (
    page: number = 1,
    limit: number = 20,
    filters?: {
      search?: string;
      status?: FishingSpotStatus;
      spotType?: FishingSpotType;
      createdById?: string;
      isPublic?: boolean;
    }
  ): Promise<{ spots: FishingSpot[]; total: number }> {
    try {
      const queryBuilder = this.fishingSpotRepository
        .createQueryBuilder('spot')
        .leftJoinAndSelect('spot.createdBy', 'createdBy');

      // Apply filters
      if (filters?.status) {
        queryBuilder.andWhere('spot.status = :status', { status: filters.status });
      }

      if (filters?.spotType) {
        queryBuilder.andWhere('spot.spotType = :spotType', { spotType: filters.spotType });
      }

      if (filters?.createdById) {
        queryBuilder.andWhere('spot.createdById = :createdById', { createdById: filters.createdById });
      }

      if (filters?.isPublic !== undefined) {
        queryBuilder.andWhere("spot.access ->> 'isPublic' = :isPublic", {
          isPublic: filters.isPublic.toString()
        });
      }

      if (filters?.search) {
        queryBuilder.andWhere(
          '(spot.name ILIKE :search OR spot.description ILIKE :search)',
          { search: `%${filters.search}%` }
        );
      }

      queryBuilder
        .orderBy('spot.createdAt', 'DESC')
        .skip((page - 1) * limit)
        .take(limit);

      const [entities, total] = await queryBuilder.getManyAndCount();
      const spots = entities.map(entity => this.entityToFishingSpot(entity));

      return { spots, total };
    } catch (error) {
      this.logger.error(`Failed to find fishing spots: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Geospatial queries for nearby spots
  async findNearbyFishingSpots (
    latitude: number,
    longitude: number,
    radiusKm: number = 10,
    limit: number = 20
  ): Promise<FishingSpot[]> {
    try {
      // Using PostgreSQL's earth distance function for geospatial queries
      // This requires the earthdistance extension to be enabled
      const spots = await this.fishingSpotRepository
        .createQueryBuilder('spot')
        .leftJoinAndSelect('spot.createdBy', 'createdBy')
        .where('spot.status = :status', { status: FishingSpotStatus.Active })
        .andWhere("spot.access ->> 'isPublic' = 'true'")
        .andWhere(
          `earth_distance(
            ll_to_earth((spot.location ->> 'latitude')::float, (spot.location ->> 'longitude')::float),
            ll_to_earth(:lat, :lng)
          ) <= :radius`,
          {
            lat: latitude,
            lng: longitude,
            radius: radiusKm * 1000, // Convert km to meters
          }
        )
        .orderBy(
          `earth_distance(
            ll_to_earth((spot.location ->> 'latitude')::float, (spot.location ->> 'longitude')::float),
            ll_to_earth(:lat, :lng)
          )`,
          'ASC'
        )
        .setParameters({ lat: latitude, lng: longitude })
        .limit(limit)
        .getMany();

      return spots.map(spot => this.entityToFishingSpot(spot));
    } catch (error) {
      this.logger.error(`Failed to find nearby fishing spots: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Get fishing spots by creator
  async findFishingSpotsByCreator (
    creatorId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ spots: FishingSpot[]; total: number }> {
    return this.findFishingSpots(page, limit, { createdById: creatorId });
  }

  // Get public fishing spots only
  async findPublicFishingSpots (
    page: number = 1,
    limit: number = 20,
    search?: string
  ): Promise<{ spots: FishingSpot[]; total: number }> {
    return this.findFishingSpots(page, limit, {
      isPublic: true,
      status: FishingSpotStatus.Active,
      search
    });
  }
}
