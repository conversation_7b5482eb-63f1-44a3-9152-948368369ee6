import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, TreeRepository } from 'typeorm';
import {
  Comment,
  User,
  Post,
  CommentStatus,
  VoteType,
} from '@/types/global';
import { CommentEntity } from '../entities/comment.entity';
import { CommentVoteEntity } from '../entities/comment-vote.entity';
import { UserEntity } from '../entities/user.entity';
import { PostEntity } from '../entities/post.entity';

@Injectable()
export class CommentRepository {
  private readonly logger = new Logger(CommentRepository.name);

  constructor(
    @InjectRepository(CommentEntity)
    private readonly commentRepository: TreeRepository<CommentEntity>,
    @InjectRepository(CommentVoteEntity)
    private readonly voteRepository: Repository<CommentVoteEntity>,
  ) { }

  // Helper method to convert entity to type
  private entityToComment (entity: CommentEntity): Comment {
    return {
      id: entity.id,
      content: entity.content,
      status: entity.status,
      author: entity.author ? this.entityToUser(entity.author) : null,
      post: entity.post ? this.entityToPost(entity.post) : null,
      parentComment: entity.parentCommentId,
      moderation: undefined, // TODO: Convert entity moderation to full Moderation type when needed
      upvotes: entity.upvotes,
      downvotes: entity.downvotes,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  private entityToUser (entity: UserEntity): User {
    return {
      id: entity.id,
      uname: entity.uname,
      status: entity.status,
      email: entity.email,
      emailVerified: entity.emailVerified,
      profile: entity.profile,
      prefs: entity.prefs,
      activity: entity.activity,
      savedContent: entity.savedContent,
      subscribedCommunities: [], // Will be populated when needed
      providerAccounts: entity.providerAccounts || {},
      lastLoginAt: entity.lastLoginAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  private entityToPost (entity: PostEntity): Post {
    return {
      id: entity.id,
      title: entity.title,
      status: entity.status,
      content: entity.content,
      author: entity.author ? this.entityToUser(entity.author) : null,
      community: null, // Simplified for comment context
      fishingSpot: null,
      comments: [],
      tags: [],
      moderation: undefined, // TODO: Convert entity moderation to full Moderation type when needed
      upvotes: entity.upvotes,
      downvotes: entity.downvotes,
      views: entity.views,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  // Comment CRUD operations
  async createComment (commentData: Partial<CommentEntity>): Promise<Comment> {
    try {
      const comment = this.commentRepository.create(commentData);
      const savedComment = await this.commentRepository.save(comment);

      // Load the comment with all relations
      const commentWithRelations = await this.commentRepository.findOne({
        where: { id: savedComment.id },
        relations: ['author', 'post', 'post.author'],
      });

      this.logger.log(`Created comment: ${savedComment.id} on post ${savedComment.postId}`);
      return this.entityToComment(commentWithRelations);
    } catch (error) {
      this.logger.error(`Failed to create comment: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findCommentById (id: string): Promise<Comment | null> {
    try {
      const comment = await this.commentRepository.findOne({
        where: { id },
        relations: ['author', 'post', 'post.author'],
      });

      return comment ? this.entityToComment(comment) : null;
    } catch (error) {
      this.logger.error(`Failed to find comment by ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateComment (id: string, updateData: Partial<CommentEntity>): Promise<Comment> {
    try {
      await this.commentRepository.update(id, {
        ...updateData,
        updatedAt: new Date(),
      });

      const updatedComment = await this.commentRepository.findOne({
        where: { id },
        relations: ['author', 'post', 'post.author'],
      });

      if (!updatedComment) {
        throw new Error('Comment not found after update');
      }

      this.logger.log(`Updated comment: ${id}`);
      return this.entityToComment(updatedComment);
    } catch (error) {
      this.logger.error(`Failed to update comment ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deleteComment (id: string): Promise<void> {
    try {
      await this.commentRepository.delete(id);
      this.logger.log(`Deleted comment: ${id}`);
    } catch (error) {
      this.logger.error(`Failed to delete comment ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Comment listing and search for a post
  async findCommentsByPost (
    postId: string,
    page: number = 1,
    limit: number = 20,
    filters?: {
      status?: CommentStatus;
      search?: string;
    }
  ): Promise<{ comments: Comment[]; total: number }> {
    try {
      const queryBuilder = this.commentRepository
        .createQueryBuilder('comment')
        .leftJoinAndSelect('comment.author', 'author')
        .leftJoinAndSelect('comment.post', 'post')
        .leftJoinAndSelect('post.author', 'postAuthor')
        .where('comment.postId = :postId', { postId });

      // Apply filters
      if (filters?.status) {
        queryBuilder.andWhere('comment.status = :status', { status: filters.status });
      } else {
        // Default to published comments only
        queryBuilder.andWhere('comment.status = :status', { status: CommentStatus.Published });
      }

      if (filters?.search) {
        queryBuilder.andWhere('comment.content ILIKE :search', { search: `%${filters.search}%` });
      }

      queryBuilder
        .orderBy('comment.createdAt', 'ASC')
        .skip((page - 1) * limit)
        .take(limit);

      const [entities, total] = await queryBuilder.getManyAndCount();
      const comments = entities.map(entity => this.entityToComment(entity));

      return { comments, total };
    } catch (error) {
      this.logger.error(`Failed to find comments for post ${postId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Threaded comment operations
  async findCommentTree (postId: string): Promise<Comment[]> {
    try {
      // Get all comments for the post in tree structure
      const tree = await this.commentRepository.findTrees({
        relations: ['author', 'post', 'post.author'],
      });

      // Filter for the specific post and convert to Comment type
      const postComments = tree.filter(comment => comment.postId === postId);
      return this.convertTreeToComments(postComments);
    } catch (error) {
      this.logger.error(`Failed to find comment tree for post ${postId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findCommentReplies (parentCommentId: string): Promise<Comment[]> {
    try {
      const parentComment = await this.commentRepository.findOne({
        where: { id: parentCommentId },
        relations: ['author', 'post', 'post.author'],
      });

      if (!parentComment) {
        throw new Error('Parent comment not found');
      }

      const descendants = await this.commentRepository.findDescendantsTree(parentComment, {
        relations: ['author', 'post', 'post.author'],
      });

      return this.convertTreeToComments([descendants]);
    } catch (error) {
      this.logger.error(`Failed to find replies for comment ${parentCommentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private convertTreeToComments (entities: CommentEntity[]): Comment[] {
    return entities.map(entity => {
      const comment = this.entityToComment(entity);
      // Note: Tree structure would need additional handling for nested replies
      // This is a simplified version for now
      return comment;
    });
  }

  // Comment voting operations
  async voteOnComment (userId: string, commentId: string, voteType: VoteType): Promise<void> {
    try {
      // Check if user already voted on this comment
      const existingVote = await this.voteRepository.findOne({
        where: { userId, commentId },
      });

      if (existingVote) {
        if (existingVote.voteType === voteType) {
          // Same vote type, remove the vote
          await this.voteRepository.delete(existingVote.id);
          await this.updateVoteCounts(commentId);
          this.logger.log(`Removed ${voteType} vote from comment ${commentId} by user ${userId}`);
          return;
        } else {
          // Different vote type, update the vote
          await this.voteRepository.update(existingVote.id, { voteType });
          await this.updateVoteCounts(commentId);
          this.logger.log(`Changed vote to ${voteType} on comment ${commentId} by user ${userId}`);
          return;
        }
      }

      // Create new vote
      const vote = this.voteRepository.create({
        userId,
        commentId,
        voteType,
      });

      await this.voteRepository.save(vote);
      await this.updateVoteCounts(commentId);
      this.logger.log(`Added ${voteType} vote to comment ${commentId} by user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to vote on comment: ${error.message}`, error.stack);
      throw error;
    }
  }

  async removeVote (userId: string, commentId: string): Promise<void> {
    try {
      await this.voteRepository.delete({ userId, commentId });
      await this.updateVoteCounts(commentId);
      this.logger.log(`Removed vote from comment ${commentId} by user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to remove vote: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getUserVote (userId: string, commentId: string): Promise<VoteType | null> {
    try {
      const vote = await this.voteRepository.findOne({
        where: { userId, commentId },
      });

      return vote ? vote.voteType : null;
    } catch (error) {
      this.logger.error(`Failed to get user vote: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async updateVoteCounts (commentId: string): Promise<void> {
    try {
      const upvotes = await this.voteRepository.count({
        where: { commentId, voteType: VoteType.Upvote },
      });

      const downvotes = await this.voteRepository.count({
        where: { commentId, voteType: VoteType.Downvote },
      });

      await this.commentRepository.update(commentId, { upvotes, downvotes });
    } catch (error) {
      this.logger.error(`Failed to update vote counts for comment ${commentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Comment statistics and analytics
  async getCommentVoteStats (commentId: string): Promise<{ upvotes: number; downvotes: number; userVote?: VoteType }> {
    try {
      const comment = await this.commentRepository.findOne({
        where: { id: commentId },
        select: ['upvotes', 'downvotes'],
      });

      if (!comment) {
        throw new Error('Comment not found');
      }

      return {
        upvotes: comment.upvotes,
        downvotes: comment.downvotes,
      };
    } catch (error) {
      this.logger.error(`Failed to get comment vote stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Comment moderation methods
  async moderateComment (commentId: string, moderationData: any): Promise<Comment> {
    try {
      await this.commentRepository.update(commentId, {
        moderation: moderationData,
        updatedAt: new Date(),
      });

      const moderatedComment = await this.commentRepository.findOne({
        where: { id: commentId },
        relations: ['author', 'post', 'post.author'],
      });

      if (!moderatedComment) {
        throw new Error('Comment not found after moderation');
      }

      this.logger.log(`Moderated comment: ${commentId}`);
      return this.entityToComment(moderatedComment);
    } catch (error) {
      this.logger.error(`Failed to moderate comment ${commentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateCommentStatus (commentId: string, status: CommentStatus): Promise<Comment> {
    try {
      await this.commentRepository.update(commentId, {
        status,
        updatedAt: new Date(),
      });

      const updatedComment = await this.commentRepository.findOne({
        where: { id: commentId },
        relations: ['author', 'post', 'post.author'],
      });

      if (!updatedComment) {
        throw new Error('Comment not found after status update');
      }

      this.logger.log(`Updated comment status to ${status}: ${commentId}`);
      return this.entityToComment(updatedComment);
    } catch (error) {
      this.logger.error(`Failed to update comment status ${commentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Find comments by author
  async findCommentsByAuthor (
    authorId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ comments: Comment[]; total: number }> {
    try {
      const queryBuilder = this.commentRepository
        .createQueryBuilder('comment')
        .leftJoinAndSelect('comment.author', 'author')
        .leftJoinAndSelect('comment.post', 'post')
        .leftJoinAndSelect('post.author', 'postAuthor')
        .where('comment.authorId = :authorId', { authorId })
        .andWhere('comment.status = :status', { status: CommentStatus.Published });

      queryBuilder
        .orderBy('comment.createdAt', 'DESC')
        .skip((page - 1) * limit)
        .take(limit);

      const [entities, total] = await queryBuilder.getManyAndCount();
      const comments = entities.map(entity => this.entityToComment(entity));

      return { comments, total };
    } catch (error) {
      this.logger.error(`Failed to find comments by author ${authorId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
