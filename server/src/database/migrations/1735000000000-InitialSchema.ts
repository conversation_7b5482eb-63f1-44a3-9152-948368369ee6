import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialSchema1735000000000 implements MigrationInterface {
  name = 'InitialSchema1735000000000';

  public async up (queryRunner: QueryRunner): Promise<void> {
    // Create enums
    await queryRunner.query(`
      CREATE TYPE "user_status_enum" AS ENUM('Active', 'Inactive', 'Banned', 'Deleted')
    `);
    await queryRunner.query(`
      CREATE TYPE "auth_provider_enum" AS ENUM('google', 'facebook', 'github', 'apple')
    `);
    await queryRunner.query(`
      CREATE TYPE "community_visibility_enum" AS ENUM('Public', 'Private', 'Restricted')
    `);
    await queryRunner.query(`
      CREATE TYPE "invite_permission_enum" AS ENUM('Anyone', 'Members', 'Moderators', 'Admins')
    `);
    await queryRunner.query(`
      CREATE TYPE "post_moderation_enum" AS ENUM('None', 'PreModeration', 'PostModeration')
    `);
    await queryRunner.query(`
      CREATE TYPE "member_role_enum" AS ENUM('Member', 'Moderator', 'Admin')
    `);
    await queryRunner.query(`
      CREATE TYPE "post_status_enum" AS ENUM('Draft', 'Published', 'Archived', 'Deleted', 'Flagged', 'UnderReview')
    `);
    await queryRunner.query(`
      CREATE TYPE "comment_status_enum" AS ENUM('Published', 'Deleted', 'Flagged', 'UnderReview')
    `);
    await queryRunner.query(`
      CREATE TYPE "vote_type_enum" AS ENUM('Upvote', 'Downvote')
    `);
    await queryRunner.query(`
      CREATE TYPE "fishing_spot_type_enum" AS ENUM('Lake', 'River', 'Ocean', 'Pond', 'Stream', 'Bay', 'Reservoir', 'Other')
    `);
    await queryRunner.query(`
      CREATE TYPE "fishing_spot_status_enum" AS ENUM('Active', 'Inactive', 'Private', 'Closed')
    `);
    await queryRunner.query(`
      CREATE TYPE "invite_status_enum" AS ENUM('Pending', 'Accepted', 'Rejected')
    `);

    // Create users table
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "uname" character varying(50) NOT NULL,
        "status" "user_status_enum" NOT NULL DEFAULT 'Active',
        "email" character varying NOT NULL,
        "email_verified" boolean NOT NULL DEFAULT false,
        "profile" jsonb NOT NULL,
        "prefs" jsonb NOT NULL,
        "activity" jsonb NOT NULL,
        "saved_content" jsonb NOT NULL DEFAULT '[]',
        "provider_accounts" jsonb,
        "last_login_at" TIMESTAMP,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_users" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_users_uname" UNIQUE ("uname"),
        CONSTRAINT "UQ_users_email" UNIQUE ("email")
      )
    `);

    // Create auth_tokens table
    await queryRunner.query(`
      CREATE TABLE "auth_tokens" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid NOT NULL,
        "provider" "auth_provider_enum" NOT NULL,
        "provider_user_id" character varying NOT NULL,
        "access_token" text NOT NULL,
        "refresh_token" text,
        "expires_at" TIMESTAMP NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_auth_tokens" PRIMARY KEY ("id")
      )
    `);

    // Create sessions table
    await queryRunner.query(`
      CREATE TABLE "sessions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid NOT NULL,
        "token" text NOT NULL,
        "ip_address" character varying,
        "user_agent" text,
        "expires_at" TIMESTAMP NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_sessions" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_sessions_token" UNIQUE ("token")
      )
    `);

    // Create communities table
    await queryRunner.query(`
      CREATE TABLE "communities" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(100) NOT NULL,
        "description" text NOT NULL,
        "rules" text NOT NULL,
        "visibility" "community_visibility_enum" NOT NULL DEFAULT 'Public',
        "invite_permission" "invite_permission_enum" NOT NULL DEFAULT 'Moderators',
        "post_moderation" "post_moderation_enum" NOT NULL DEFAULT 'None',
        "comment_moderation" "post_moderation_enum" NOT NULL DEFAULT 'None',
        "pic" character varying,
        "banner" character varying,
        "owner_id" uuid NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_communities" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_communities_name" UNIQUE ("name")
      )
    `);

    // Create tags table
    await queryRunner.query(`
      CREATE TABLE "tags" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(50) NOT NULL,
        "description" character varying(100),
        "category" character varying(50),
        "color" character varying NOT NULL DEFAULT '#3B82F6',
        "usage_count" integer NOT NULL DEFAULT 0,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_tags" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_tags_name" UNIQUE ("name")
      )
    `);

    // Create fishing_spots table
    await queryRunner.query(`
      CREATE TABLE "fishing_spots" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(200) NOT NULL,
        "description" text NOT NULL,
        "spot_type" "fishing_spot_type_enum" NOT NULL,
        "status" "fishing_spot_status_enum" NOT NULL DEFAULT 'Active',
        "location" jsonb NOT NULL,
        "conditions" jsonb,
        "access" jsonb,
        "created_by_id" uuid NOT NULL,
        "rating" integer NOT NULL DEFAULT 0,
        "review_count" integer NOT NULL DEFAULT 0,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_fishing_spots" PRIMARY KEY ("id")
      )
    `);

    // Create posts table
    await queryRunner.query(`
      CREATE TABLE "posts" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "title" character varying(200) NOT NULL,
        "status" "post_status_enum" NOT NULL DEFAULT 'Published',
        "content" text,
        "author_id" uuid NOT NULL,
        "community_id" uuid NOT NULL,
        "fishing_spot_id" uuid,
        "moderation" jsonb,
        "upvotes" integer NOT NULL DEFAULT 0,
        "downvotes" integer NOT NULL DEFAULT 0,
        "views" integer NOT NULL DEFAULT 0,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_posts" PRIMARY KEY ("id")
      )
    `);

    // Create comments table
    await queryRunner.query(`
      CREATE TABLE "comments" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "content" text NOT NULL,
        "status" "comment_status_enum" NOT NULL DEFAULT 'Published',
        "author_id" uuid NOT NULL,
        "post_id" uuid NOT NULL,
        "parent_comment_id" uuid,
        "moderation" jsonb,
        "upvotes" integer NOT NULL DEFAULT 0,
        "downvotes" integer NOT NULL DEFAULT 0,
        "mpath" character varying DEFAULT '',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_comments" PRIMARY KEY ("id")
      )
    `);

    // Create community_members table
    await queryRunner.query(`
      CREATE TABLE "community_members" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid NOT NULL,
        "community_id" uuid NOT NULL,
        "role" "member_role_enum" NOT NULL DEFAULT 'Member',
        "joined_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_community_members" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_community_members_user_community" UNIQUE ("user_id", "community_id")
      )
    `);

    // Create community_invites table
    await queryRunner.query(`
      CREATE TABLE "community_invites" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "community_id" uuid NOT NULL,
        "invited_by_id" uuid NOT NULL,
        "invited_user_id" uuid NOT NULL,
        "role" "member_role_enum" NOT NULL DEFAULT 'Member',
        "status" "invite_status_enum" NOT NULL DEFAULT 'Pending',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_community_invites" PRIMARY KEY ("id")
      )
    `);

    // Create user_follows table
    await queryRunner.query(`
      CREATE TABLE "user_follows" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "follower_id" uuid NOT NULL,
        "following_id" uuid NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_user_follows" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_user_follows_follower_following" UNIQUE ("follower_id", "following_id")
      )
    `);

    // Create post_votes table
    await queryRunner.query(`
      CREATE TABLE "post_votes" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid NOT NULL,
        "post_id" uuid NOT NULL,
        "vote_type" "vote_type_enum" NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_post_votes" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_post_votes_user_post" UNIQUE ("user_id", "post_id")
      )
    `);

    // Create comment_votes table
    await queryRunner.query(`
      CREATE TABLE "comment_votes" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid NOT NULL,
        "comment_id" uuid NOT NULL,
        "vote_type" "vote_type_enum" NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_comment_votes" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_comment_votes_user_comment" UNIQUE ("user_id", "comment_id")
      )
    `);

    // Create post_tags junction table
    await queryRunner.query(`
      CREATE TABLE "post_tags" (
        "post_id" uuid NOT NULL,
        "tag_id" uuid NOT NULL,
        CONSTRAINT "PK_post_tags" PRIMARY KEY ("post_id", "tag_id")
      )
    `);

    // Create community_subscribers junction table
    await queryRunner.query(`
      CREATE TABLE "community_subscribers" (
        "community_id" uuid NOT NULL,
        "user_id" uuid NOT NULL,
        CONSTRAINT "PK_community_subscribers" PRIMARY KEY ("community_id", "user_id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "auth_tokens" ADD CONSTRAINT "FK_auth_tokens_user"
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "sessions" ADD CONSTRAINT "FK_sessions_user"
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "communities" ADD CONSTRAINT "FK_communities_owner"
      FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "fishing_spots" ADD CONSTRAINT "FK_fishing_spots_created_by"
      FOREIGN KEY ("created_by_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "posts" ADD CONSTRAINT "FK_posts_author"
      FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "posts" ADD CONSTRAINT "FK_posts_community"
      FOREIGN KEY ("community_id") REFERENCES "communities"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "posts" ADD CONSTRAINT "FK_posts_fishing_spot"
      FOREIGN KEY ("fishing_spot_id") REFERENCES "fishing_spots"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "comments" ADD CONSTRAINT "FK_comments_author"
      FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "comments" ADD CONSTRAINT "FK_comments_post"
      FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "comments" ADD CONSTRAINT "FK_comments_parent"
      FOREIGN KEY ("parent_comment_id") REFERENCES "comments"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "community_members" ADD CONSTRAINT "FK_community_members_user"
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "community_members" ADD CONSTRAINT "FK_community_members_community"
      FOREIGN KEY ("community_id") REFERENCES "communities"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "community_invites" ADD CONSTRAINT "FK_community_invites_community"
      FOREIGN KEY ("community_id") REFERENCES "communities"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "community_invites" ADD CONSTRAINT "FK_community_invites_invited_by"
      FOREIGN KEY ("invited_by_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "community_invites" ADD CONSTRAINT "FK_community_invites_invited_user"
      FOREIGN KEY ("invited_user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "user_follows" ADD CONSTRAINT "FK_user_follows_follower"
      FOREIGN KEY ("follower_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "user_follows" ADD CONSTRAINT "FK_user_follows_following"
      FOREIGN KEY ("following_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "post_votes" ADD CONSTRAINT "FK_post_votes_user"
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "post_votes" ADD CONSTRAINT "FK_post_votes_post"
      FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "comment_votes" ADD CONSTRAINT "FK_comment_votes_user"
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "comment_votes" ADD CONSTRAINT "FK_comment_votes_comment"
      FOREIGN KEY ("comment_id") REFERENCES "comments"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "post_tags" ADD CONSTRAINT "FK_post_tags_post"
      FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "post_tags" ADD CONSTRAINT "FK_post_tags_tag"
      FOREIGN KEY ("tag_id") REFERENCES "tags"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "community_subscribers" ADD CONSTRAINT "FK_community_subscribers_community"
      FOREIGN KEY ("community_id") REFERENCES "communities"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "community_subscribers" ADD CONSTRAINT "FK_community_subscribers_user"
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    // Create indexes for performance and graph queries
    await queryRunner.query(`CREATE INDEX "IDX_users_email" ON "users" ("email")`);
    await queryRunner.query(`CREATE INDEX "IDX_users_uname" ON "users" ("uname")`);
    await queryRunner.query(`CREATE INDEX "IDX_users_provider_accounts" ON "users" USING GIN ("provider_accounts")`);

    await queryRunner.query(`CREATE INDEX "IDX_auth_tokens_user_id" ON "auth_tokens" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_auth_tokens_provider_user" ON "auth_tokens" ("provider", "provider_user_id")`);

    await queryRunner.query(`CREATE INDEX "IDX_sessions_user_id" ON "sessions" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_sessions_token" ON "sessions" ("token")`);
    await queryRunner.query(`CREATE INDEX "IDX_sessions_expires_at" ON "sessions" ("expires_at")`);

    await queryRunner.query(`CREATE INDEX "IDX_communities_name" ON "communities" ("name")`);
    await queryRunner.query(`CREATE INDEX "IDX_communities_visibility" ON "communities" ("visibility")`);
    await queryRunner.query(`CREATE INDEX "IDX_communities_owner_id" ON "communities" ("owner_id")`);

    await queryRunner.query(`CREATE INDEX "IDX_posts_author_id" ON "posts" ("author_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_posts_community_id" ON "posts" ("community_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_posts_status" ON "posts" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_posts_created_at" ON "posts" ("created_at")`);
    await queryRunner.query(`CREATE INDEX "IDX_posts_upvotes" ON "posts" ("upvotes")`);
    await queryRunner.query(`CREATE INDEX "IDX_posts_views" ON "posts" ("views")`);

    await queryRunner.query(`CREATE INDEX "IDX_comments_author_id" ON "comments" ("author_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_comments_post_id" ON "comments" ("post_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_comments_status" ON "comments" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_comments_created_at" ON "comments" ("created_at")`);
    await queryRunner.query(`CREATE INDEX "IDX_comments_parent_comment_id" ON "comments" ("parent_comment_id")`);

    await queryRunner.query(`CREATE INDEX "IDX_user_follows_follower_id" ON "user_follows" ("follower_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_user_follows_following_id" ON "user_follows" ("following_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_user_follows_created_at" ON "user_follows" ("created_at")`);

    await queryRunner.query(`CREATE INDEX "IDX_post_votes_user_id" ON "post_votes" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_post_votes_post_id" ON "post_votes" ("post_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_post_votes_vote_type" ON "post_votes" ("vote_type")`);
    await queryRunner.query(`CREATE INDEX "IDX_post_votes_created_at" ON "post_votes" ("created_at")`);

    await queryRunner.query(`CREATE INDEX "IDX_comment_votes_user_id" ON "comment_votes" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_comment_votes_comment_id" ON "comment_votes" ("comment_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_comment_votes_vote_type" ON "comment_votes" ("vote_type")`);
    await queryRunner.query(`CREATE INDEX "IDX_comment_votes_created_at" ON "comment_votes" ("created_at")`);

    await queryRunner.query(`CREATE INDEX "IDX_tags_name" ON "tags" ("name")`);
    await queryRunner.query(`CREATE INDEX "IDX_tags_category" ON "tags" ("category")`);

    await queryRunner.query(`CREATE INDEX "IDX_fishing_spots_name" ON "fishing_spots" ("name")`);
    await queryRunner.query(`CREATE INDEX "IDX_fishing_spots_status" ON "fishing_spots" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_fishing_spots_location" ON "fishing_spots" USING GIN ("location")`);
    await queryRunner.query(`CREATE INDEX "IDX_fishing_spots_created_by_id" ON "fishing_spots" ("created_by_id")`);

    await queryRunner.query(`CREATE INDEX "IDX_community_members_user_id" ON "community_members" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_community_members_community_id" ON "community_members" ("community_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_community_members_role" ON "community_members" ("role")`);
    await queryRunner.query(`CREATE INDEX "IDX_community_members_joined_at" ON "community_members" ("joined_at")`);

    await queryRunner.query(`CREATE INDEX "IDX_community_invites_community_id" ON "community_invites" ("community_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_community_invites_invited_by_id" ON "community_invites" ("invited_by_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_community_invites_invited_user_id" ON "community_invites" ("invited_user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_community_invites_status" ON "community_invites" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_community_invites_created_at" ON "community_invites" ("created_at")`);
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    // Drop junction tables first
    await queryRunner.query(`DROP TABLE "community_subscribers"`);
    await queryRunner.query(`DROP TABLE "post_tags"`);

    // Drop tables with foreign keys
    await queryRunner.query(`DROP TABLE "comment_votes"`);
    await queryRunner.query(`DROP TABLE "post_votes"`);
    await queryRunner.query(`DROP TABLE "user_follows"`);
    await queryRunner.query(`DROP TABLE "community_invites"`);
    await queryRunner.query(`DROP TABLE "community_members"`);
    await queryRunner.query(`DROP TABLE "comments"`);
    await queryRunner.query(`DROP TABLE "posts"`);
    await queryRunner.query(`DROP TABLE "fishing_spots"`);
    await queryRunner.query(`DROP TABLE "tags"`);
    await queryRunner.query(`DROP TABLE "communities"`);
    await queryRunner.query(`DROP TABLE "sessions"`);
    await queryRunner.query(`DROP TABLE "auth_tokens"`);
    await queryRunner.query(`DROP TABLE "users"`);

    // Drop enums
    await queryRunner.query(`DROP TYPE "invite_status_enum"`);
    await queryRunner.query(`DROP TYPE "fishing_spot_status_enum"`);
    await queryRunner.query(`DROP TYPE "fishing_spot_type_enum"`);
    await queryRunner.query(`DROP TYPE "vote_type_enum"`);
    await queryRunner.query(`DROP TYPE "comment_status_enum"`);
    await queryRunner.query(`DROP TYPE "post_status_enum"`);
    await queryRunner.query(`DROP TYPE "member_role_enum"`);
    await queryRunner.query(`DROP TYPE "post_moderation_enum"`);
    await queryRunner.query(`DROP TYPE "invite_permission_enum"`);
    await queryRunner.query(`DROP TYPE "community_visibility_enum"`);
    await queryRunner.query(`DROP TYPE "auth_provider_enum"`);
    await queryRunner.query(`DROP TYPE "user_status_enum"`);
  }
}
