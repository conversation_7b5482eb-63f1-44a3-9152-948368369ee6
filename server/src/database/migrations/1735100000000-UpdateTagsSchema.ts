import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTagsSchema1735100000000 implements MigrationInterface {
  name = 'UpdateTagsSchema1735100000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create tag status enum
    await queryRunner.query(`
      CREATE TYPE "tag_status_enum" AS ENUM('Pending', 'Approved', 'Rejected')
    `);

    // Create a temporary table with the new structure
    await queryRunner.query(`
      CREATE TABLE "tags_new" (
        "name" character varying(50) NOT NULL,
        "display_name" character varying(50) NOT NULL,
        "description" character varying(100),
        "category" character varying(50),
        "color" character varying NOT NULL DEFAULT '#3B82F6',
        "status" "tag_status_enum" NOT NULL DEFAULT 'Pending',
        "usage_count" integer NOT NULL DEFAULT 0,
        "created_by" uuid NOT NULL,
        "moderated_by" uuid,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "moderated_at" TIMESTAMP,
        CONSTRAINT "PK_tags_new" PRIMARY KEY ("name")
      )
    `);

    // Create indexes on the new table
    await queryRunner.query(`CREATE INDEX "IDX_tags_new_status" ON "tags_new" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_tags_new_category" ON "tags_new" ("category")`);
    await queryRunner.query(`CREATE INDEX "IDX_tags_new_created_by" ON "tags_new" ("created_by")`);

    // Migrate existing data (if any exists)
    // Note: This assumes we have a default user to assign as creator
    // In a real migration, you'd want to handle this more carefully
    await queryRunner.query(`
      INSERT INTO "tags_new" (
        "name", 
        "display_name", 
        "description", 
        "category", 
        "color", 
        "status",
        "usage_count", 
        "created_by",
        "created_at", 
        "updated_at"
      )
      SELECT 
        LOWER(REPLACE("name", ' ', '-')) as "name",
        "name" as "display_name",
        "description",
        "category",
        "color",
        'Approved'::"tag_status_enum" as "status",
        "usage_count",
        (SELECT "id" FROM "users" LIMIT 1) as "created_by",
        "created_at",
        "updated_at"
      FROM "tags"
      WHERE EXISTS (SELECT 1 FROM "users" LIMIT 1)
    `);

    // Create a temporary post_tags table with new structure
    await queryRunner.query(`
      CREATE TABLE "post_tags_new" (
        "post_id" uuid NOT NULL,
        "tag_name" character varying(50) NOT NULL,
        CONSTRAINT "PK_post_tags_new" PRIMARY KEY ("post_id", "tag_name")
      )
    `);

    // Migrate post_tags data
    await queryRunner.query(`
      INSERT INTO "post_tags_new" ("post_id", "tag_name")
      SELECT pt."post_id", LOWER(REPLACE(t."name", ' ', '-'))
      FROM "post_tags" pt
      INNER JOIN "tags" t ON pt."tag_id" = t."id"
    `);

    // Drop old tables and constraints
    await queryRunner.query(`DROP TABLE "post_tags"`);
    await queryRunner.query(`DROP TABLE "tags"`);

    // Rename new tables
    await queryRunner.query(`ALTER TABLE "tags_new" RENAME TO "tags"`);
    await queryRunner.query(`ALTER TABLE "post_tags_new" RENAME TO "post_tags"`);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "tags" 
      ADD CONSTRAINT "FK_tags_created_by" 
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "tags" 
      ADD CONSTRAINT "FK_tags_moderated_by" 
      FOREIGN KEY ("moderated_by") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "post_tags" 
      ADD CONSTRAINT "FK_post_tags_post_id" 
      FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "post_tags" 
      ADD CONSTRAINT "FK_post_tags_tag_name" 
      FOREIGN KEY ("tag_name") REFERENCES "tags"("name") ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Create old tags table structure
    await queryRunner.query(`
      CREATE TABLE "tags_old" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(50) NOT NULL,
        "description" character varying(100),
        "category" character varying(50),
        "color" character varying NOT NULL DEFAULT '#3B82F6',
        "usage_count" integer NOT NULL DEFAULT 0,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_tags_old" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_tags_old_name" UNIQUE ("name")
      )
    `);

    // Create old post_tags table structure
    await queryRunner.query(`
      CREATE TABLE "post_tags_old" (
        "post_id" uuid NOT NULL,
        "tag_id" uuid NOT NULL,
        CONSTRAINT "PK_post_tags_old" PRIMARY KEY ("post_id", "tag_id")
      )
    `);

    // Migrate data back (this will lose some information like status, creator, etc.)
    await queryRunner.query(`
      INSERT INTO "tags_old" ("name", "description", "category", "color", "usage_count", "created_at", "updated_at")
      SELECT "display_name", "description", "category", "color", "usage_count", "created_at", "updated_at"
      FROM "tags"
    `);

    await queryRunner.query(`
      INSERT INTO "post_tags_old" ("post_id", "tag_id")
      SELECT pt."post_id", t."id"
      FROM "post_tags" pt
      INNER JOIN "tags_old" t ON pt."tag_name" = LOWER(REPLACE(t."name", ' ', '-'))
    `);

    // Drop new tables
    await queryRunner.query(`DROP TABLE "post_tags"`);
    await queryRunner.query(`DROP TABLE "tags"`);

    // Rename old tables back
    await queryRunner.query(`ALTER TABLE "tags_old" RENAME TO "tags"`);
    await queryRunner.query(`ALTER TABLE "post_tags_old" RENAME TO "post_tags"`);

    // Recreate indexes
    await queryRunner.query(`CREATE INDEX "IDX_tags_name" ON "tags" ("name")`);
    await queryRunner.query(`CREATE INDEX "IDX_tags_category" ON "tags" ("category")`);

    // Drop the enum
    await queryRunner.query(`DROP TYPE "tag_status_enum"`);
  }
}
