import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  Index,
} from 'typeorm';
import { PostEntity } from './post.entity';

@Entity('tags')
@Index(['name'])
@Index(['category'])
export class TagEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, length: 50 })
  @Index()
  name: string;

  @Column({ length: 100, nullable: true })
  description?: string;

  @Column({ length: 50, nullable: true })
  @Index()
  category?: string;

  @Column({ default: '#3B82F6' })
  color: string;

  @Column({ name: 'usage_count', default: 0 })
  usageCount: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToMany(() => PostEntity, (post) => post.tags)
  posts: PostEntity[];
}
