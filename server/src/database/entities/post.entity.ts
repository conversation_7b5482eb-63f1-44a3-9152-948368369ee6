import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinColumn,
  JoinTable,
  Index,
} from 'typeorm';
import { PostStatus } from '@/types/global';
import { UserEntity } from './user.entity';
import { CommunityEntity } from './community.entity';
import { CommentEntity } from './comment.entity';
import { TagEntity } from './tag.entity';
import { FishingSpotEntity } from './fishing-spot.entity';
import { PostVoteEntity } from './post-vote.entity';

@Entity('posts')
@Index(['authorId'])
@Index(['communityId'])
@Index(['status'])
@Index(['createdAt'])
@Index(['upvotes'])
@Index(['views'])
export class PostEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 200 })
  title: string;

  @Column({
    type: 'enum',
    enum: PostStatus,
    default: PostStatus.Published,
  })
  @Index()
  status: PostStatus;

  @Column({ type: 'text', nullable: true })
  content?: string;

  @Column({ name: 'author_id' })
  authorId: string;

  @Column({ name: 'community_id' })
  communityId: string;

  @Column({ name: 'fishing_spot_id', nullable: true })
  fishingSpotId?: string;

  // Moderation data as JSONB
  @Column({ type: 'jsonb', nullable: true })
  moderation?: {
    moderatedBy: string; // User ID
    moderatedAt: Date;
    action: string;
    logs: Array<{
      id: string;
      message: string;
      createdBy: string; // User ID
      createdAt: Date;
    }>;
  };

  @Column({ default: 0 })
  @Index()
  upvotes: number;

  @Column({ default: 0 })
  downvotes: number;

  @Column({ default: 0 })
  @Index()
  views: number;

  @CreateDateColumn({ name: 'created_at' })
  @Index()
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => UserEntity, (user) => user.posts, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'author_id' })
  author: UserEntity;

  @ManyToOne(() => CommunityEntity, (community) => community.posts, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'community_id' })
  community: CommunityEntity;

  @ManyToOne(() => FishingSpotEntity, (spot) => spot.posts, { nullable: true })
  @JoinColumn({ name: 'fishing_spot_id' })
  fishingSpot?: FishingSpotEntity;

  @OneToMany(() => CommentEntity, (comment) => comment.post)
  comments: CommentEntity[];

  @OneToMany(() => PostVoteEntity, (vote) => vote.post)
  votes: PostVoteEntity[];

  // Many-to-many relationship with tags
  @ManyToMany(() => TagEntity, (tag) => tag.posts)
  @JoinTable({
    name: 'post_tags',
    joinColumn: { name: 'post_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'tag_id', referencedColumnName: 'id' },
  })
  tags: TagEntity[];
}
